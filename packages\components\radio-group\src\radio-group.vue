<template>
  <el-radio-group
    v-bind="props"
    :model-value="modelValue"
    @update:model-value="handleUpdateModelValue"
    @change="handleChange"
  >
    <!-- 通过 options 配置生成的选项 -->
    <el-radio v-for="option in options" :key="option.value" v-bind="option">
      {{ option.label }}
    </el-radio>
  </el-radio-group>
</template>

<script lang="ts" setup>
import { neRadioGroupProps } from './radio-group'

defineOptions({
  name: 'NeRadioGroup',
  inheritAttrs: false,
})

const props = defineProps(neRadioGroupProps)
const emit = defineEmits(['update:modelValue', 'change'])

// 处理值更新
const handleUpdateModelValue = (value: any) => {
  emit('update:modelValue', value)
}

// 处理变化事件
const handleChange = (value: any) => {
  emit('change', value)
}
</script>
