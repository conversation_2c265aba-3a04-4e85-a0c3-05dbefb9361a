export default {
  name: 'hi',
  el: {
    breadcrumb: {
      label: 'ब्रेडक्रंब',
    },
    colorpicker: {
      confirm: 'ठीक है',
      clear: 'हटाएँ',
      defaultLabel: 'कलर पिकर',
      description:
        'मौजूदा रंग {color} है. कोई नया रंग चुनने के लिए एंटर दबाएँ.',
      alphaLabel: 'अल्फा मान चुनें',
    },
    datepicker: {
      now: 'अभी',
      today: 'आज',
      cancel: 'कैंसिल करें',
      clear: 'हटाएँ',
      confirm: 'ठीक है',
      dateTablePrompt:
        'महीने का दिन चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',
      monthTablePrompt:
        'महीने चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें ',
      yearTablePrompt:
        'साल चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',
      selectedDate: 'चुनी गई तारीख',
      selectDate: 'तारीख चुनें',
      selectTime: 'समय चुनें',
      startDate: 'शुरू होने की तारीख',
      startTime: 'शुरू होने का समय',
      endDate: 'खत्म होने की तारीख',
      endTime: 'खत्म होने का समय',
      prevYear: 'पिछला साल',
      nextYear: 'अगला साल',
      prevMonth: 'पिछला महीना',
      nextMonth: 'अगला महीना',
      year: '',
      month1: 'जनवरी',
      month2: 'फरवरी',
      month3: 'मार्च',
      month4: 'अप्रैल',
      month5: 'मई',
      month6: 'जून',
      month7: 'जुलाई',
      month8: 'अगस्त',
      month9: 'सितंबर',
      month10: 'अक्टूबर',
      month11: 'नवंबर',
      month12: 'दिसंबर',
      week: 'सप्ताह',
      weeks: {
        sun: 'रवि',
        mon: 'सोम',
        tue: 'मंगल',
        wed: 'बुध',
        thu: 'गुरु',
        fri: 'शुक्र',
        sat: 'शनि',
      },
      weeksFull: {
        sun: 'रविवार',
        mon: 'सोमवार',
        tue: 'मंगलवार',
        wed: 'बुधवार',
        thu: 'गुरुवार',
        fri: 'शुक्रवार',
        sat: 'शनिवार',
      },
      months: {
        jan: 'जन.',
        feb: 'फर.',
        mar: 'मार्च',
        apr: 'अप्रैल',
        may: 'मई',
        jun: 'जून',
        jul: 'जुलाई',
        aug: 'अग.',
        sep: 'सितं.',
        oct: 'अक्तू.',
        nov: 'नवं.',
        dec: 'दिसं.',
      },
    },
    inputNumber: {
      decrease: 'संख्या घटाएँ',
      increase: 'संख्या बढ़ाएँ',
    },
    select: {
      loading: 'लोड हो रहा है',
      noMatch: 'कोई मैचिंग डेटा नहीं है',
      noData: 'कोई डेटा नहीं है',
      placeholder: 'चुनें',
    },
    mention: {
      loading: 'लोड हो रहा है',
    },
    dropdown: {
      toggleDropdown: 'ड्रॉपडाउन को टॉगल करें',
    },
    cascader: {
      noMatch: 'कोई मैचिंग डेटा नहीं है',
      loading: 'लोड हो रहा है',
      placeholder: 'चुनें',
      noData: 'कोई डेटा नहीं है',
    },
    pagination: {
      goto: 'पर जाएँ',
      pagesize: '/पेज',
      total: 'कुल {total}',
      pageClassifier: '',
      page: 'पेज',
      prev: 'पिछले पेज पर जाएँ',
      next: 'अगले पेज पर जाएँ',
      currentPage: 'पेज {pager}',
      prevPages: 'पिछले {pager} पेज',
      nextPages: 'अगले {pager} पेज',
      deprecationWarning:
        'पुरानी पद्धति के उपयोग का पता चला, अधिक जानकारी के लिए एल-पेजिनेशन का डॉक्यूमेंटेशन देखें',
    },
    dialog: {
      close: 'यह डायलॉग बंद करें',
    },
    drawer: {
      close: 'यह डायलॉग बंद करें',
    },
    messagebox: {
      title: 'मैसेज',
      confirm: 'ठीक है',
      cancel: 'कैंसिल करें',
      error: 'अवैध इनपुट',
      close: 'यह डायलॉग बंद करें',
    },
    upload: {
      deleteTip: 'हटाने के लिए डिलीट दबाएँ',
      delete: 'हटाएँ',
      preview: 'प्रीव्यू',
      continue: 'जारी रखें',
    },
    slider: {
      defaultLabel: '{min} और {max} के बीच स्लाइडर',
      defaultRangeStartLabel: 'शुरूआती वैल्यू चुनें',
      defaultRangeEndLabel: 'समाप्ति की वैल्यू चुनें',
    },
    table: {
      emptyText: 'कोई डेटा नहीं है',
      confirmFilter: 'पुष्टि करें',
      resetFilter: 'रीसेट करें',
      clearFilter: 'सभी',
      sumText: 'जोड़े',
    },
    tour: {
      next: 'अगला',
      previous: 'पिछला',
      finish: 'पूरा करें',
    },
    tree: {
      emptyText: 'कोई डेटा नहीं है',
    },
    transfer: {
      noMatch: 'कोई मैचिंग डेटा नहीं है',
      noData: 'कोई डेटा नहीं है',
      titles: ['लिस्ट 1', 'लिस्ट 2'],
      filterPlaceholder: 'कीवर्ड डालें',
      noCheckedFormat: '{total} आइटम',
      hasCheckedFormat: '{checked}/{total} चेक किया गया',
    },
    image: {
      error: 'नहीं हो सका',
    },
    pageHeader: {
      title: 'पीछे जाएँ ',
    },
    popconfirm: {
      confirmButtonText: 'हाँ',
      cancelButtonText: 'नहीं',
    },
    carousel: {
      leftArrow: 'कैरोसेल तीर बाएँ',
      rightArrow: 'कैरोसेल तीर दाएँ',
      indicator: 'कैरोसेल इंडेक्स {index} पर स्विच करें',
    },
  },
}
