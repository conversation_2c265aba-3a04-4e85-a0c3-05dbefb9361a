{"name": "@neue-plus/resolver", "version": "1.0.0", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --dts --format esm,cjs"}, "peerDependencies": {"unplugin-vue-components": "^0.25.0"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.0.0"}}