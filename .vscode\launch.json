{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}, {"name": "Launch Edge", "request": "launch", "type": "msedge", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}, {"name": "Run and debug", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "pnpm", "runtimeArgs": ["dev"]}]}