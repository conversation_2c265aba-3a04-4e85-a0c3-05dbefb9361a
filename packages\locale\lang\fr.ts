export default {
  name: 'fr',
  el: {
    breadcrumb: {
      label: `<PERSON><PERSON> d'Ariane`,
    },
    colorpicker: {
      confirm: 'Confirmer',
      clear: 'Effacer',
      defaultLabel: 'color picker',
      description:
        'La couleur actuelle est {color}. Appuyer sur Entrée pour sélectionner une nouvelle couleur.',
    },
    datepicker: {
      now: 'Maintenant',
      today: 'Auj.',
      cancel: 'Annule<PERSON>',
      clear: 'Effacer',
      confirm: 'Confirmer',
      dateTablePrompt:
        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le jour du mois',
      monthTablePrompt:
        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le mois',
      yearTablePrompt:
        "Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner l'année",
      selectedDate: 'Date sélectionnée',
      selectDate: 'Choisir date',
      selectTime: 'Choisir horaire',
      startDate: 'Date début',
      startTime: '<PERSON><PERSON>re début',
      endDate: 'Date fin',
      endTime: '<PERSON><PERSON>re fin',
      prevYear: '<PERSON><PERSON> précédente',
      nextYear: '<PERSON><PERSON> suivante',
      prevMonth: 'Mois précédent',
      nextMonth: 'Mois suivant',
      year: '', // In french, like in english, we don't say "Année" after the year number.
      month1: 'Janvier',
      month2: 'Février',
      month3: 'Mars',
      month4: 'Avril',
      month5: 'Mai',
      month6: 'Juin',
      month7: 'Juillet',
      month8: 'Août',
      month9: 'Septembre',
      month10: 'Octobre',
      month11: 'Novembre',
      month12: 'Décembre',
      week: 'Semaine',
      weeks: {
        sun: 'Dim',
        mon: 'Lun',
        tue: 'Mar',
        wed: 'Mer',
        thu: 'Jeu',
        fri: 'Ven',
        sat: 'Sam',
      },
      weeksFull: {
        sun: 'Dimanche',
        mon: 'Lundi',
        tue: 'Mardi',
        wed: 'Mercredi',
        thu: 'Jeudi',
        fri: 'Vendredi',
        sat: 'Samedi',
      },
      months: {
        jan: 'Jan',
        feb: 'Fév',
        mar: 'Mar',
        apr: 'Avr',
        may: 'Mai',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aoû',
        sep: 'Sep',
        oct: 'Oct',
        nov: 'Nov',
        dec: 'Déc',
      },
    },
    inputNumber: {
      decrease: 'décrémenter',
      increase: 'incrémenter',
    },
    select: {
      loading: 'Chargement',
      noMatch: 'Aucune correspondance',
      noData: 'Aucune donnée',
      placeholder: 'Choisir',
    },
    mention: {
      loading: 'Chargement',
    },
    cascader: {
      noMatch: 'Aucune correspondance',
      loading: 'Chargement',
      placeholder: 'Choisir',
      noData: 'Aucune donnée',
    },
    pagination: {
      goto: 'Aller à',
      pagesize: '/page',
      total: 'Total {total}',
      pageClassifier: '',
      page: 'Page',
      prev: 'Aller à la page précédente',
      next: 'Aller à la page suivante',
      currentPage: 'page {pager}',
      prevPages: '{pager} pages précédentes',
      nextPages: '{pager} pages suivantes',
      deprecationWarning:
        'Utilisations obsolètes détectées, veuillez vous référer à la documentation el-pagination pour plus de détails',
    },
    dialog: {
      close: 'Fermer la boîte de dialogue',
    },
    drawer: {
      close: 'Fermer la boîte de dialogue',
    },
    messagebox: {
      title: 'Message',
      confirm: 'Confirmer',
      cancel: 'Annuler',
      error: 'Erreur',
      close: 'Fermer la boîte de dialogue',
    },
    upload: {
      deleteTip: 'Cliquer sur supprimer pour retirer le fichier',
      delete: 'Supprimer',
      preview: 'Aperçu',
      continue: 'Continuer',
    },
    slider: {
      defaultLabel: 'curseur entre {min} et {max}',
      defaultRangeStartLabel: 'choisir la valeur de départ',
      defaultRangeEndLabel: 'sélectionner la valeur finale',
    },
    table: {
      emptyText: 'Aucune donnée',
      confirmFilter: 'Confirmer',
      resetFilter: 'Réinitialiser',
      clearFilter: 'Tous',
      sumText: 'Somme',
    },
    tour: {
      next: 'suivant',
      previous: 'précédent',
      finish: 'fin',
    },
    tree: {
      emptyText: 'Aucune donnée',
    },
    transfer: {
      noMatch: 'Aucune correspondance',
      noData: 'Aucune donnée',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Entrer un mot clef',
      noCheckedFormat: '{total} elements',
      hasCheckedFormat: '{checked}/{total} coché(s)',
    },
    image: {
      error: 'ECHEC',
    },
    pageHeader: {
      title: 'Retour',
    },
    popconfirm: {
      confirmButtonText: 'Oui',
      cancelButtonText: 'Non',
    },
    carousel: {
      leftArrow: 'Flèche du carrousel vers la gauche',
      rightArrow: 'Flèche du carrousel vers la droite',
      indicator: 'Passer au carrousel index {index}',
    },
  },
}
