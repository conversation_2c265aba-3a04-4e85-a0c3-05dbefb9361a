import { buildProp, buildProps } from '@neue-plus/utils'
import {
  ElementEvent,
  PageEvents,
  SchemaApiConfig,
  SchemaElement,
} from '../types'
import type { ExtractPropTypes } from 'vue'

export const neMaterialRenderProps = buildProps({
  // 配置对象
  config: buildProp({
    type: Object as () => {
      configProvider?: Record<string, any>
      events?: ElementEvent[]
      api?: Record<string, any>
    },
    default: () => ({ configProvider: {}, events: [], api: {} }),
  }),
  events: buildProp({
    type: Array as () => PageEvents[],
    default: () => [],
  }),
  apis: buildProp({
    type: Object as () => Record<string, SchemaApiConfig>,
    default: () => ({}),
  }),
  elements: buildProp({
    type: Array as () => SchemaElement[],
    default: () => [],
  }),
} as const)

export type NeMaterialRenderProps = ExtractPropTypes<
  typeof neMaterialRenderProps
>

// 事件类型
export type NeMaterialRenderEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
