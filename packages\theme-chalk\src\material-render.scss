@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(material-render) {
  width: 100%;
  position: relative;

  // 动画模式
  @include m(animated) {
    @include e(item) {
      transition: all var(--ne-animation-duration, 300ms) ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: getCssVar('box-shadow-light');
      }
    }
  }

  // 懒加载模式
  @include m(lazy) {
    @include e(item) {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.6s ease, transform 0.6s ease;

      &.is-loaded {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  // 项目容器
  @include e(item) {
    margin-bottom: getCssVar('spacing-sm', 8px);
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    // 动画项目
    @include m(animated) {
      transition: all var(--ne-animation-duration, 300ms) ease;
      border-radius: getCssVar('border-radius-base');

      &:hover {
        transform: translateY(-1px);
        box-shadow: getCssVar('box-shadow-base');
      }
    }

    // 加载状态
    @include m(loading) {
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(getCssVar('color-primary-rgb'), 0.2),
          transparent
        );
        animation: loading-shimmer 1.5s infinite;
        z-index: 1;
      }
    }

    // 错误状态
    @include m(error) {
      border: 1px solid getCssVar('color-danger');
      border-radius: getCssVar('border-radius-base');
      background-color: getCssVar('color-danger-light-9');
    }
  }

  // 错误提示
  @include e(error) {
    padding: getCssVar('spacing-sm', 8px) getCssVar('spacing-md', 12px);
    background-color: getCssVar('color-danger-light-9');
    border: 1px solid getCssVar('color-danger-light-5');
    border-radius: getCssVar('border-radius-base');
    color: getCssVar('color-danger');
    font-size: getCssVar('font-size-small');
    margin: getCssVar('spacing-xs', 4px) 0;
    display: flex;
    align-items: center;
    gap: getCssVar('spacing-xs', 4px);

    &::before {
      content: '⚠';
      font-weight: bold;
    }

    &:hover {
      background-color: getCssVar('color-danger-light-7');
      cursor: pointer;
    }
  }

  // 加载指示器
  @include e(loading) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: getCssVar('spacing-lg', 16px);
    color: getCssVar('text-color-secondary');
    font-size: getCssVar('font-size-small');

    &::before {
      content: '';
      width: 16px;
      height: 16px;
      border: 2px solid getCssVar('border-color-light');
      border-top-color: getCssVar('color-primary');
      border-radius: 50%;
      animation: loading-spin 1s linear infinite;
      margin-right: getCssVar('spacing-sm', 8px);
    }
  }

  // 空状态
  @include e(empty) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: getCssVar('spacing-xxl', 24px);
    color: getCssVar('text-color-secondary');
    font-size: getCssVar('font-size-base');

    &::before {
      content: '📄';
      font-size: 48px;
      margin-bottom: getCssVar('spacing-md', 12px);
      opacity: 0.5;
    }
  }

  // 材料类型样式
  @include e(text) {
    line-height: 1.6;
    color: getCssVar('text-color-regular');
    word-wrap: break-word;

    &.is-primary {
      color: getCssVar('color-primary');
      font-weight: 500;
    }

    &.is-success {
      color: getCssVar('color-success');
    }

    &.is-warning {
      color: getCssVar('color-warning');
    }

    &.is-danger {
      color: getCssVar('color-danger');
    }

    &.is-info {
      color: getCssVar('color-info');
    }
  }

  @include e(image) {
    max-width: 100%;
    height: auto;
    border-radius: getCssVar('border-radius-base');
    transition: getCssVar('transition-all');

    &:hover {
      transform: scale(1.02);
      box-shadow: getCssVar('box-shadow-base');
    }

    &.is-loading {
      background-color: getCssVar('fill-color-light');
      background-image: linear-gradient(
        45deg,
        transparent 25%,
        rgba(getCssVar('color-primary-rgb'), 0.1) 25%,
        rgba(getCssVar('color-primary-rgb'), 0.1) 50%,
        transparent 50%,
        transparent 75%,
        rgba(getCssVar('color-primary-rgb'), 0.1) 75%
      );
      background-size: 20px 20px;
      animation: loading-stripes 1s linear infinite;
    }

    &.is-error {
      border: 2px dashed getCssVar('color-danger');
      background-color: getCssVar('color-danger-light-9');
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100px;
      color: getCssVar('color-danger');

      &::before {
        content: '🖼️ 图片加载失败';
        font-size: getCssVar('font-size-small');
      }
    }
  }

  @include e(video) {
    width: 100%;
    border-radius: getCssVar('border-radius-base');
    background-color: getCssVar('fill-color-dark');

    &:focus {
      outline: 2px solid getCssVar('color-primary');
      outline-offset: 2px;
    }
  }

  @include e(component) {
    // 组件容器的基础样式
    position: relative;

    &.is-loading {
      opacity: 0.6;
      pointer-events: none;
    }

    &.is-error {
      border: 1px dashed getCssVar('color-danger');
      border-radius: getCssVar('border-radius-base');
      padding: getCssVar('spacing-md', 12px);
      background-color: getCssVar('color-danger-light-9');
    }
  }

  // 响应式设计
  @media (max-width: getCssVar('breakpoint-sm', 576px)) {
    @include e(item) {
      margin-bottom: getCssVar('spacing-xs', 4px);
    }

    @include e(image) {
      &:hover {
        transform: none;
      }
    }
  }

  // 暗黑模式适配
  @at-root {
    .dark & {
      @include e(error) {
        background-color: rgba(getCssVar('color-danger-rgb'), 0.1);
        border-color: rgba(getCssVar('color-danger-rgb'), 0.3);
        color: getCssVar('color-danger-light-3');
      }

      @include e(loading) {
        color: getCssVar('text-color-primary');
      }

      @include e(empty) {
        color: getCssVar('text-color-primary');
      }

      @include e(text) {
        color: getCssVar('text-color-primary');
      }

      @include e(image) {
        &.is-loading {
          background-color: getCssVar('fill-color');
        }

        &.is-error {
          background-color: rgba(getCssVar('color-danger-rgb'), 0.1);
          border-color: rgba(getCssVar('color-danger-rgb'), 0.3);
          color: getCssVar('color-danger-light-3');
        }
      }

      @include e(video) {
        background-color: getCssVar('fill-color');
      }

      @include e(component) {
        &.is-error {
          background-color: rgba(getCssVar('color-danger-rgb'), 0.1);
          border-color: rgba(getCssVar('color-danger-rgb'), 0.3);
        }
      }
    }
  }
}

// 动画定义
@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}
