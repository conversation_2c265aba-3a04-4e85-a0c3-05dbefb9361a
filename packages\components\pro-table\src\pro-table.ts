import { buildProp, buildProps } from '@neue-plus/utils'
import type { PaginationProps, TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import type { DynamicFormConfig } from '../dynamic-form/src/dynamic-form'

// ProTable列配置，扩展了筛选功能
export interface ProTableColumn {
  prop?: string
  label?: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  // 筛选相关
  filterable?: boolean
  filterType?: 'input' | 'select' | 'date' | 'daterange'
  filterOptions?: Array<{ label: string; value: any }>
  filterPlaceholder?: string
  // 隐藏列
  hide?: boolean
}

// 分页配置
export interface ProTablePagination extends Partial<PaginationProps> {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

// 筛选表单数据
export interface ProTableFilters {
  [key: string]: any
}

export const proTableProps = buildProps({
  // 表格数据
  data: buildProp({
    type: Array,
    default: () => [],
  }),
  // 列配置
  columns: buildProp({
    type: Array as () => ProTableColumn[],
    default: () => [],
    required: true,
  }),
  // 是否显示筛选表单
  showFilter: buildProp({
    type: Boolean,
    default: true,
  }),
  // 搜索表单配置
  searchConfig: buildProp({
    type: Object as () => DynamicFormConfig,
  }),
  // 是否显示分页
  showPagination: buildProp({
    type: Boolean,
    default: true,
  }),
  // 分页配置
  pagination: buildProp({
    type: Object as () => ProTablePagination,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true,
    }),
  }),
  // 是否加载中
  loading: buildProp({
    type: Boolean,
    default: false,
  }),
  // 是否显示边框
  bordered: buildProp({
    type: Boolean,
    default: false,
  }),
  // 表格大小
  size: buildProp({
    type: String,
    values: ['large', 'default', 'small'],
    default: 'default',
  }),
} as const)

export type ProTableProps = ExtractPropTypes<typeof proTableProps> &
  Omit<TableProps<any>, 'data'>

// 事件类型 - 使用函数类型定义
export type ProTableEmits = {
  (e: 'filter-change', filters: ProTableFilters): void
  (e: 'page-change', current: number, pageSize: number): void
  (
    e: 'sort-change',
    prop: string,
    order: 'ascending' | 'descending' | null
  ): void
  (e: 'refresh'): void
}
