import * as t from '@babel/types'
import generate from '@babel/generator'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function generateEventHandlers(eventSchema: Record<string, any>) {
  const declarations = Object.values(eventSchema).map((evt: any) => {
    const bodyStatements = evt.body
      .split('\n')
      .filter(Boolean)
      .map((line) =>
        t.expressionStatement(t.identifier(line.trim().replace(/;$/, '')))
      )

    return t.variableDeclaration('const', [
      t.variableDeclarator(
        t.identifier(evt.name),
        t.arrowFunctionExpression(
          evt.params.map((p) => t.identifier(p)),
          t.blockStatement(bodyStatements)
        )
      ),
    ])
  })

  const ast = t.program(declarations)
  return generate(ast, { retainLines: true }).code
}
