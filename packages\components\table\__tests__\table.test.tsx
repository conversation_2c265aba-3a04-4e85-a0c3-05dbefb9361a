import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import Table from '../src/table.vue'

const AXIOM = '<PERSON><PERSON> is the best girl'

describe('Table.vue', () => {
  test('render test', () => {
    const wrapper = mount(() => <Table>{AXIOM}</Table>)
    expect(wrapper.text()).toEqual(AXIOM)
  })

  test('should render table with data', () => {
    const tableData = [
      { name: '<PERSON>', age: 20 },
      { name: '<PERSON>', age: 18 },
    ]

    const wrapper = mount(() => (
      <Table data={tableData}>
        <el-table-column prop="name" label="Name" />
        <el-table-column prop="age" label="Age" />
      </Table>
    ))

    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('should pass props to el-table', () => {
    const wrapper = mount(() => (
      <Table stripe border>
        <el-table-column prop="name" label="Name" />
      </Table>
    ))

    expect(wrapper.find('.el-table--striped').exists()).toBe(true)
    expect(wrapper.find('.el-table--border').exists()).toBe(true)
  })
})
