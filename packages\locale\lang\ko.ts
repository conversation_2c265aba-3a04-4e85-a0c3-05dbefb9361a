export default {
  name: 'ko',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: '확인',
      clear: '초기화',
      defaultLabel: '색상 선택기',
      description:
        '현재 색상은 {color}입니다. Enter 키를 눌러 새 색상을 선택합니다.',
    },
    datepicker: {
      now: '지금',
      today: '오늘',
      cancel: '취소',
      clear: '초기화',
      confirm: '확인',
      dateTablePrompt: '화살표 키를 사용하고 Enter를 눌러 날짜를 선택하십시오.',
      monthTablePrompt: '화살표 키를 사용하고 Enter를 눌러 월을 선택합니다.',
      yearTablePrompt:
        '화살표 키를 사용하고 Enter 키를 눌러 연도를 선택합니다.',
      selectDate: '날짜 선택',
      selectTime: '시간 선택',
      startDate: '시작 날짜',
      startTime: '시작 시간',
      endDate: '종료 날짜',
      endTime: '종료 시간',
      prevYear: '지난해',
      nextYear: '다음해',
      prevMonth: '지난달',
      nextMonth: '다음달',
      year: '년',
      month1: '1월',
      month2: '2월',
      month3: '3월',
      month4: '4월',
      month5: '5월',
      month6: '6월',
      month7: '7월',
      month8: '8월',
      month9: '9월',
      month10: '10월',
      month11: '11월',
      month12: '12월',
      // week: 'week',
      weeks: {
        sun: '일',
        mon: '월',
        tue: '화',
        wed: '수',
        thu: '목',
        fri: '금',
        sat: '토',
      },
      months: {
        jan: '1월',
        feb: '2월',
        mar: '3월',
        apr: '4월',
        may: '5월',
        jun: '6월',
        jul: '7월',
        aug: '8월',
        sep: '9월',
        oct: '10월',
        nov: '11월',
        dec: '12월',
      },
    },
    inputNumber: {
      decrease: '값 증가',
      increase: '값 감소',
    },
    select: {
      loading: '불러오는 중',
      noMatch: '검색된 데이터 없음',
      noData: '데이터 없음',
      placeholder: '선택',
    },
    mention: {
      loading: '불러오는 중',
    },
    dropdown: {
      toggleDropdown: '드롭다운 전환',
    },
    cascader: {
      noMatch: '검색된 데이터 없음',
      loading: '불러오는 중',
      placeholder: '선택',
      noData: '데이터 없음',
    },
    pagination: {
      goto: '이동',
      pagesize: '건/페이지',
      total: '총 {total} 건',
      pageClassifier: '페이지로',
      page: '페이지',
      prev: '이전 페이지로 이동',
      next: '다음 페이지로 이동',
      currentPage: '페이지 {pager}',
      prevPages: '이전 {pager} 페이지',
      nextPages: '다음 {pager} 페이지',
      deprecationWarning:
        '더 이상 사용되지 않는 동작이 감지되었습니다. 자세한 내용은 el-pagination 문서를 참조하세요.',
    },
    dialog: {
      close: '대화 상자 닫기',
    },
    drawer: {
      close: '대화 상자 닫기',
    },
    messagebox: {
      title: '메시지',
      confirm: '확인',
      cancel: '취소',
      error: '올바르지 않은 입력',
      close: '대화 상자 닫기',
    },
    upload: {
      deleteTip: 'Delete 키를 눌러 삭제',
      delete: '삭제',
      preview: '미리보기',
      continue: '계속하기',
    },
    slider: {
      defaultLabel: '{min}과 {max} 사이의 슬라이더',
      defaultRangeStartLabel: '시작 값 선택',
      defaultRangeEndLabel: '종료 값 선택',
    },
    table: {
      emptyText: '데이터 없음',
      confirmFilter: '확인',
      resetFilter: '초기화',
      clearFilter: '전체',
      sumText: '합계',
    },
    tour: {
      next: '다음',
      previous: '이전',
      finish: '종료',
    },
    tree: {
      emptyText: '데이터 없음',
    },
    transfer: {
      noMatch: '검색된 데이터 없음',
      noData: '데이터 없음',
      titles: ['리스트 1', '리스트 2'],
      filterPlaceholder: '검색어를 입력하세요',
      noCheckedFormat: '총 {total} 건',
      hasCheckedFormat: '{checked}/{total} 선택됨',
    },
    image: {
      error: '불러오기 실패',
    },
    pageHeader: {
      title: '뒤로',
    },
    popconfirm: {
      confirmButtonText: '예',
      cancelButtonText: '아니오',
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
