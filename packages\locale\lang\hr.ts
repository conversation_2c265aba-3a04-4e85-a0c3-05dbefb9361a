export default {
  name: 'hr',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: 'OK',
      clear: '<PERSON><PERSON><PERSON><PERSON>',
    },
    datepicker: {
      now: '<PERSON><PERSON>',
      today: '<PERSON><PERSON>',
      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON>',
      confirm: 'OK',
      selectDate: 'Odaberi datum',
      selectTime: 'Odaberi vrijeme',
      startDate: 'Datum početka',
      startTime: 'Vri<PERSON>me početka',
      endDate: 'Datum završetka',
      endTime: 'Vrijeme završetka',
      prevYear: 'Preth<PERSON>na godina',
      nextYear: 'Sljedeća godina',
      prevMonth: 'Prethodni mjesec',
      nextMonth: 'Slje<PERSON>ći mjesec',
      year: '',
      month1: 'Siječanj',
      month2: 'Veljača',
      month3: 'O<PERSON><PERSON><PERSON>',
      month4: 'Travanj',
      month5: 'Svibanj',
      month6: '<PERSON>panj',
      month7: 'Srpanj',
      month8: '<PERSON><PERSON><PERSON>',
      month9: 'Rujan',
      month10: 'Listopad',
      month11: '<PERSON><PERSON><PERSON>',
      month12: 'Prosinac',
      week: 'tjedan',
      weeks: {
        sun: 'Ned',
        mon: 'Pon',
        tue: 'Uto',
        wed: 'Sri',
        thu: 'Čet',
        fri: 'Pet',
        sat: 'Sub',
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'May',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Oct',
        nov: 'Nov',
        dec: 'Dec',
      },
    },
    select: {
      loading: 'Učitavanje',
      noMatch: 'Nema pronađenih podataka',
      noData: 'Nema podataka',
      placeholder: 'Izaberi',
    },
    mention: {
      loading: 'Učitavanje',
    },
    cascader: {
      noMatch: 'Nema pronađenih podataka',
      loading: 'Učitavanje',
      placeholder: 'Izaberi',
      noData: 'Nema podataka',
    },
    pagination: {
      goto: 'Idi na',
      pagesize: '/stranica',
      total: 'Ukupno {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
    },
    messagebox: {
      title: 'Poruka',
      confirm: 'OK',
      cancel: 'Otkaži',
      error: 'Pogrešan unos',
    },
    upload: {
      deleteTip: 'pritisnite izbriši za brisanje',
      delete: 'Izbriši',
      preview: 'Pregled',
      continue: 'Nastavak',
    },
    table: {
      emptyText: 'Nema podataka',
      confirmFilter: 'Potvrdi',
      resetFilter: 'Resetiraj',
      clearFilter: 'Sve',
      sumText: 'Suma',
    },
    tree: {
      emptyText: 'Nema podataka',
    },
    transfer: {
      noMatch: 'Nema pronađenih podataka',
      noData: 'Nema podataka',
      titles: ['Lista 1', 'Lista 2'], // to be translated
      filterPlaceholder: 'Unesite ključnu riječ', // to be translated
      noCheckedFormat: '{total} stavki', // to be translated
      hasCheckedFormat: '{checked}/{total} checked', // to be translated
    },
    image: {
      error: 'FAILED', // to be translated
    },
    pageHeader: {
      title: 'Back', // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No', // to be translated
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
