<template>
  <el-checkbox-group
    v-bind="props"
    :model-value="modelValue"
    @update:model-value="handleUpdateModelValue"
    @change="handleChange"
  >
    <!-- 通过 options 配置生成的选项 -->
    <el-checkbox v-for="option in options" :key="option.value" v-bind="option">
      {{ option.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script lang="ts" setup>
import { neCheckboxGroupProps } from './checkbox-group'

defineOptions({
  name: 'NeCheckboxGroup',
  inheritAttrs: false,
})

const props = defineProps(neCheckboxGroupProps)
const emit = defineEmits(['update:modelValue', 'change'])

// 处理值更新
const handleUpdateModelValue = (value: any) => {
  emit('update:modelValue', value)
}

// 处理变化事件
const handleChange = (value: any) => {
  emit('change', value)
}
</script>
