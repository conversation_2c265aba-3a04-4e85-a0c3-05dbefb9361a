export default {
  name: 'mg',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: 'EN<PERSON>',
      clear: '<PERSON><PERSON><PERSON>',
    },
    datepicker: {
      now: '<PERSON><PERSON>',
      today: '<PERSON><PERSON>ny',
      cancel: '<PERSON><PERSON><PERSON><PERSON>',
      clear: '<PERSON><PERSON><PERSON>',
      confirm: 'ENY',
      selectDate: 'Misafidy daty',
      selectTime: 'Misafidy ora',
      startDate: '<PERSON><PERSON> fanombohana',
      startTime: '<PERSON>a fanombohana',
      endDate: '<PERSON>ty farany',
      endTime: 'Ora farany',
      prevYear: 'Taona teo aloha',
      nextYear: '<PERSON>na manaraka',
      prevMonth: '<PERSON>ana teo aloha',
      nextMonth: '<PERSON>ana manaraka',
      year: '',
      month1: 'Janoary',
      month2: 'Febroary',
      month3: 'Martsa',
      month4: 'Aprily',
      month5: 'May',
      month6: 'Jona',
      month7: 'Jolay',
      month8: 'A<PERSON><PERSON><PERSON>',
      month9: 'Septambra',
      month10: 'Ok<PERSON><PERSON>',
      month11: 'Nova<PERSON><PERSON>',
      month12: '<PERSON><PERSON><PERSON>',
      week: 'herinandro',
      weeks: {
        sun: 'Lad',
        mon: 'Ala',
        tue: 'Tal',
        wed: 'Lar',
        thu: 'Lak',
        fri: 'Zom',
        sat: 'Sab',
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'May',
        jun: 'Jon',
        jul: 'Jol',
        aug: 'Aog',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Des',
      },
    },
    select: {
      loading: 'Eo ampiandrasana',
      noMatch: 'Tsy misy angona mifanentana',
      noData: 'Tsy misy angona',
      placeholder: 'Safidy',
    },
    mention: {
      loading: 'Eo ampiandrasana',
    },
    cascader: {
      noMatch: 'Tsy misy angona mifanentana',
      loading: 'Eo ampiandrasana',
      placeholder: 'Safidy',
      noData: 'Tsy misy angona',
    },
    pagination: {
      goto: 'Mandeha any',
      pagesize: '/page',
      total: 'Totaly {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
      deprecationWarning:
        'Fampiasana tsy ampiasaina intsony no hita, azafady mba jereo ny tahirin-kevitra el-pagination raha mila fanazavana fanampiny',
    },
    messagebox: {
      title: 'Hafatra',
      confirm: 'ENY',
      cancel: 'Hanafoana',
      error: 'Fampidirana tsy ara-dalàna',
    },
    upload: {
      deleteTip: 'tsindrio fafana raha hanala',
      delete: 'Fafana',
      preview: 'Topi-maso',
      continue: 'Hanoy',
    },
    table: {
      emptyText: 'Tsy misy angona',
      confirmFilter: 'Manamarina',
      resetFilter: 'Averina',
      clearFilter: 'Rehetra',
      sumText: 'Atambatra',
    },
    tree: {
      emptyText: 'Tsy misy angona',
    },
    transfer: {
      noMatch: 'Tsy misy angona mifanentana',
      noData: 'Tsy misy angona',
      titles: ['Lisitra 1', 'Lisitra 2'], // to be translated
      filterPlaceholder: 'Ampidiro teny fanalahidy', // to be translated
      noCheckedFormat: '{total} zavatra', // to be translated
      hasCheckedFormat: '{checked}/{total} voamarina', // to be translated
    },
    image: {
      error: 'TSY NAHOMBY',
    },
    pageHeader: {
      title: 'Miverina', // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Eny',
      cancelButtonText: 'Tsy',
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
