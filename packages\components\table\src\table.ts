import { buildProp, buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'
import type { NeTableColumnProps } from './table-column/type'

// 基础table属性定义
export const neTableProps = buildProps({
  data: buildProp({
    type: Array,
    default: () => [],
  }),
  columns: buildProp({
    type: Array as () => NeTableColumnProps[],
    default: () => [],
  }),
  bordered: buildProp({
    type: Boolean,
    default: false,
  }),
  height: buildProp({
    type: [String, Number],
  }),
  maxHeight: buildProp({
    type: [String, Number],
  }),
  stripe: buildProp({
    type: Boolean,
    default: false,
  }),
  border: buildProp({
    type: Boolean,
    default: false,
  }),
  size: buildProp({
    type: String,
    values: ['large', 'default', 'small'],
  }),
  fit: buildProp({
    type: Boolean,
    default: true,
  }),
  showHeader: buildProp({
    type: Boolean,
    default: true,
  }),
  highlightCurrentRow: buildProp({
    type: Boolean,
    default: false,
  }),
  currentRowKey: buildProp({
    type: [String, Number],
  }),
  rowClassName: buildProp({
    type: [String, Function],
  }),
  rowStyle: buildProp({
    type: [Object, Function],
  }),
  cellClassName: buildProp({
    type: [String, Function],
  }),
  cellStyle: buildProp({
    type: [Object, Function],
  }),
  headerRowClassName: buildProp({
    type: [String, Function],
  }),
  headerRowStyle: buildProp({
    type: [Object, Function],
  }),
  headerCellClassName: buildProp({
    type: [String, Function],
  }),
  headerCellStyle: buildProp({
    type: [Object, Function],
  }),
  rowKey: buildProp({
    type: [String, Function],
  }),
  emptyText: buildProp({
    type: String,
  }),
  defaultExpandAll: buildProp({
    type: Boolean,
    default: false,
  }),
  expandRowKeys: buildProp({
    type: Array,
  }),
  defaultSort: buildProp({
    type: Object,
  }),
  tooltipEffect: buildProp({
    type: String,
  }),
  showSummary: buildProp({
    type: Boolean,
    default: false,
  }),
  sumText: buildProp({
    type: String,
  }),
  summaryMethod: buildProp({
    type: Function,
  }),
  spanMethod: buildProp({
    type: Function,
  }),
  selectOnIndeterminate: buildProp({
    type: Boolean,
    default: true,
  }),
  indent: buildProp({
    type: Number,
    default: 16,
  }),
  lazy: buildProp({
    type: Boolean,
    default: false,
  }),
  load: buildProp({
    type: Function,
  }),
  treeProps: buildProp({
    type: Object,
    default: () => ({
      hasChildren: 'hasChildren',
      children: 'children',
    }),
  }),
  tableLayout: buildProp({
    type: String,
    default: 'fixed',
  }),
  scrollbarAlwaysOn: buildProp({
    type: Boolean,
    default: false,
  }),
  flexible: buildProp({
    type: Boolean,
    default: false,
  }),
} as const)

export type NeTableProps = ExtractPropTypes<typeof neTableProps>
