@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: getCssVar('spacing-md', 12px);
  align-items: center;

  // 垂直布局
  @include m(vertical) {
    flex-direction: column;
    align-items: flex-start;
    gap: getCssVar('spacing-sm', 8px);

    .#{$namespace}-radio {
      margin-right: 0;
      margin-bottom: getCssVar('spacing-sm', 8px);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 带边框样式
  @include m(bordered) {
    .#{$namespace}-radio {
      border: 1px solid getCssVar('border-color-light');
      border-radius: getCssVar('border-radius-base');
      padding: getCssVar('spacing-sm', 8px) getCssVar('spacing-md', 12px);
      margin-right: 0;
      margin-bottom: getCssVar('spacing-sm', 8px);
      transition: getCssVar('transition-border');

      &:hover {
        border-color: getCssVar('color-primary');
      }

      &.is-checked {
        border-color: getCssVar('color-primary');
        background-color: getCssVar('color-primary-light-9');
      }

      &.is-disabled {
        border-color: getCssVar('border-color-lighter');
        background-color: getCssVar('fill-color-light');
        cursor: not-allowed;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 尺寸变体
  @include m(large) {
    gap: getCssVar('spacing-lg', 16px);

    .#{$namespace}-radio {
      font-size: getCssVar('font-size-large');
    }

    &.#{$namespace}-radio-group--vertical {
      gap: getCssVar('spacing-md', 12px);
    }
  }

  @include m(small) {
    gap: getCssVar('spacing-sm', 8px);

    .#{$namespace}-radio {
      font-size: getCssVar('font-size-small');
    }

    &.#{$namespace}-radio-group--vertical {
      gap: getCssVar('spacing-xs', 4px);
    }
  }

  // 禁用状态
  @include when(disabled) {
    .#{$namespace}-radio {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  // 内部单选框样式调整
  .#{$namespace}-radio {
    margin-right: 0;
    white-space: nowrap;

    &:focus-visible {
      outline: 2px solid getCssVar('color-primary');
      outline-offset: 2px;
      border-radius: getCssVar('border-radius-small');
    }

    // 单选框标签
    .#{$namespace}-radio__label {
      font-size: inherit;
      color: getCssVar('text-color-regular');
      transition: getCssVar('transition-color');
    }

    // 选中状态
    &.is-checked {
      .#{$namespace}-radio__label {
        color: getCssVar('color-primary');
        font-weight: 500;
      }
    }

    // 禁用状态
    &.is-disabled {
      .#{$namespace}-radio__label {
        color: getCssVar('text-color-disabled');
      }
    }
  }

  // 响应式设计
  @media (max-width: getCssVar('breakpoint-sm', 576px)) {
    &:not(.#{$namespace}-radio-group--vertical) {
      flex-direction: column;
      align-items: flex-start;
      gap: getCssVar('spacing-sm', 8px);
    }
  }

  // 暗黑模式适配
  @at-root {
    .dark & {
      .#{$namespace}-radio {
        .#{$namespace}-radio__label {
          color: getCssVar('text-color-primary');
        }

        &.is-checked {
          .#{$namespace}-radio__label {
            color: getCssVar('color-primary');
          }
        }

        &.is-disabled {
          .#{$namespace}-radio__label {
            color: getCssVar('text-color-disabled');
          }
        }
      }

      &.#{$namespace}-radio-group--bordered {
        .#{$namespace}-radio {
          border-color: getCssVar('border-color');
          background-color: transparent;

          &:hover {
            border-color: getCssVar('color-primary');
          }

          &.is-checked {
            border-color: getCssVar('color-primary');
            background-color: rgba(getCssVar('color-primary-rgb'), 0.1);
          }

          &.is-disabled {
            border-color: getCssVar('border-color-light');
            background-color: getCssVar('fill-color');
          }
        }
      }
    }
  }

  // 动画效果
  .#{$namespace}-radio {
    transition: all getCssVar('transition-duration-fast') getCssVar('transition-function-ease-in-out-bezier');

    &:hover:not(.is-disabled) {
      transform: translateY(-1px);
    }

    &:active:not(.is-disabled) {
      transform: translateY(0);
    }
  }

  // 自定义间距支持
  &[data-gap] {
    gap: var(--ne-radio-group-gap);
  }

  // 内联样式支持
  &.#{$namespace}-radio-group--inline {
    display: inline-flex;
    vertical-align: middle;
  }
}
