// 单个事件动作节点
export interface ActionNode {
  id: string
  type: 'start' | 'end' | 'normal'
  title: string
  content?: string
  config?: {
    actionType: string
    actionName?: string
    target?: string
    [key: string]: any
  }
  children?: ActionNode[]
}

// 元素绑定的事件
export interface ElementEvent {
  nickName: string
  eventName: string
  actions: ActionNode[]
}

// 页面钩子事件
export interface PageEvents {
  onBeforeMount?: () => void
  onMounted?: () => void
}
