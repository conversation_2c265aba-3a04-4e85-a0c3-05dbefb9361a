export const initialData = [
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
]
export const tableData = [
  {
    id: 1,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 2,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now() - 24 * 60 * 60 * 1000,
    date: '2016-05-04',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 3,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
    children: [
      {
        id: 31,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        id: 32,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
  {
    id: 4,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
]
export const pageData = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onClick: {
      name: 'onBeforeMount',
      params: ['e'],
      body: `console.log('clicked', e);\nalert('hi');`,
    },
    onChange: {
      name: 'onMounted',
      params: ['value'],
      body: `console.log('value changed', value);`,
    },
  },
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'SearchForm_60spo02i5g', // 组件唯一身份
      type: 'layout-content',
      name: '搜索表单',
      props: {
        style: {
          padding: '16px',
          width: '100%',
          background: '#fff',
          marginRight: '16px',
        },
      },
      events: [],
      elements: [
        {
          type: 'div',
          props: {
            style: { 'padding-bottom': '16px', width: '100%' },
          },
          elements: [
            {
              id: 'button_3gx13gh2ht',
              type: 'button',
              props: {
                type: 'primary',
                icon: 'Refrigerator',
              },
              slots: {
                default: '在NeueCAX中打开',
              },
            },
            {
              id: 'button_expand_toggle',
              type: 'button',
              props: {
                icon: 'Expand',
              },
              slots: {
                default: '展开/收缩',
              },
              events: [
                {
                  nickName: '展开收缩切换事件',
                  eventName: 'onClick',
                  actions: [
                    {
                      id: 'start',
                      type: 'start',
                      title: '开始',
                    },
                    {
                      id: '56132221', // 节点id
                      type: 'normal', // 节点类型
                      title: '切换展开收缩', // 节点名称
                      content: '智能切换树表格展开收缩状态', // 节点描述
                      config: {
                        actionType: 'toggleExpandAll', // 节点配置
                        actionName: '切换展开收缩',
                        target: 'MarsTable_3gx13gh2ht',
                      },
                      children: [],
                    },
                    {
                      id: 'end',
                      type: 'end',
                      title: '结束',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 'MarsTable_3gx13gh2ht',
          type: 'tree-table',
          name: '普通表格',
          props: {
            columns: [
              {
                label: '零部件编号',
                prop: 'number',
              },
              {
                label: '零部件名称',
                prop: 'name',
              },
              {
                label: '子类别',
                prop: 'subcategory',
              },
              {
                label: '版本号',
                prop: 'version',
              },
              {
                label: '状态',
                prop: 'status',
                valueType: 'tag',
                valueEnum: {
                  wangxiaohu: {
                    text: '全部',
                    type: 'danger',
                  },
                  黄文隆: { text: '付小小', type: 'info' },
                  working: { text: '工作中', type: 'success' },
                  4: { text: '林东东' },
                  5: { text: '陈帅帅' },
                  6: { text: '兼某某' },
                },
              },
              {
                label: '修改人',
                prop: 'modified_by',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modified_time',
                valueType: 'datetime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            data: [],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'openDrawer',
                    actionName: '打开drawer',
                    target: 'Drawer_er4mn6jbtk',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          methods: {},
        },
      ],
    },
    {
      id: 'Drawer_er4mn6jbtk',
      type: 'drawer',
      name: '弹框',
      props: {
        modelValue: true,
        size: '80%',
      },
      elements: [
        {
          id: 'Tabs_3gx13gh2ht',
          type: 'tabs',
          name: 'tabs',
          props: {},
          elements: [
            {
              id: 'TabPane_3gx13gh2ht',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '属性信息',
              },
              elements: [],
            },
            {
              id: 'TabPane_usage',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
            {
              id: 'TabPane_history',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '历史记录',
              },
              elements: [],
            },
            {
              id: 'TabPane_useBy',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
          ],
        },
      ],
      slots: {
        header: [
          {
            id: 'button_fb19o54fjh',
            type: 'div',
            name: '文本框',
            props: {
              style: 'font-size:20px;font-weight:600;color:#0F172A',
              innerHTML: 'UPX10007248',
            },
          },
        ],
        footer: 'footer',
      },
    },
  ],
}
