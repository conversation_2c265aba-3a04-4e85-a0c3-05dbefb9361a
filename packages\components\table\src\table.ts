import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx, TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import type { NeTableColumnProps } from './table-column/type'

// 定义列配置类型，继承el-table-column的属性
export type NeTableColumn = Partial<TableColumnCtx<any>> & NeTableColumnProps

// 只定义自定义属性，其他属性通过 v-bind="$attrs" 传递给 el-table
export const neTableProps = buildProps({
  // 自定义属性：是否显示边框
  bordered: {
    type: Boolean,
    default: false,
  },
  // 列配置数组，继承el-table-column的所有属性
  columns: {
    type: Array as () => Array<NeTableColumn>,
    default: () => [],
  },
} as const)

export type NeTableProps = ExtractPropTypes<typeof neTableProps> &
  TableProps<any>
