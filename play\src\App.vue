<template>
  <div>
    <NeCard>
      <h3>Card组件测试</h3>
      <p>这是一个基于Element Plus封装的Card组件</p>
    </NeCard>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Slot方式</h3>
      <NeTable :data="tableData" stripe border>
        <el-table-column prop="name" label="姓名" width="180" />
        <el-table-column prop="age" label="年龄" width="180" />
        <el-table-column prop="address" label="地址" />
      </NeTable>
    </div>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Columns配置</h3>
      <NeTable :data="tableData" :columns="tableColumns" stripe bordered />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
  },
])

// 表格列配置 - 继承el-table-column的所有属性
const tableColumns = ref([
  {
    prop: 'name',
    label: '姓名',
    width: 180,
    sortable: true,
    fixed: 'left' as const,
  },
  {
    prop: 'age',
    label: '年龄',
    width: 180,
    sortable: true,
    align: 'center' as const,
  },
  {
    prop: 'address',
    label: '地址',
    showOverflowTooltip: true,
    minWidth: 200,
  },
])
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
