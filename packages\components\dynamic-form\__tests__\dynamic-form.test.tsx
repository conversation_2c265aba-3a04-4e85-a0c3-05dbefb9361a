import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import DynamicForm from '../src/dynamic-form.vue'

const mockConfig = {
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input' as const,
      required: true,
      placeholder: '请输入姓名',
    },
    {
      prop: 'age',
      label: '年龄',
      type: 'number' as const,
      min: 0,
      max: 120,
    },
    {
      prop: 'gender',
      label: '性别',
      type: 'radio' as const,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
      ],
    },
  ],
  showSubmit: true,
  showReset: true,
}

const mockFormData = {
  name: '',
  age: 0,
  gender: '',
}

describe('DynamicForm.vue', () => {
  test('render test', () => {
    const wrapper = mount(DynamicForm as any, {
      props: {
        modelValue: mockFormData,
        config: mockConfig,
      },
    })

    expect(wrapper.find('.el-form').exists()).toBe(true)
    expect(wrapper.findAll('.el-form-item')).toHaveLength(4) // 3 form items + 1 button group
  })

  test('should render form items based on config', () => {
    const wrapper = mount(DynamicForm as any, {
      props: {
        modelValue: mockFormData,
        config: mockConfig,
      },
    })

    expect(wrapper.find('input[placeholder="请输入姓名"]').exists()).toBe(true)
    expect(wrapper.find('.el-input-number').exists()).toBe(true)
    expect(wrapper.find('.el-radio-group').exists()).toBe(true)
  })

  test('should show submit and reset buttons', () => {
    const wrapper = mount(DynamicForm as any, {
      props: {
        modelValue: mockFormData,
        config: mockConfig,
      },
    })

    expect(wrapper.find('.el-button--primary').exists()).toBe(true) // submit button
    expect(wrapper.find('.el-button:not(.el-button--primary)').exists()).toBe(
      true
    ) // reset button
  })

  test('should hide buttons when showSubmit is false', () => {
    const configWithoutButtons = {
      ...mockConfig,
      showSubmit: false,
      showReset: false,
    }

    const wrapper = mount(DynamicForm as any, {
      props: {
        modelValue: mockFormData,
        config: configWithoutButtons,
      },
    })

    expect(wrapper.findAll('.el-form-item')).toHaveLength(3) // only form items, no button group
  })

  test('should support inline layout', () => {
    const inlineConfig = {
      ...mockConfig,
      layout: 'inline' as const,
    }

    const wrapper = mount(DynamicForm as any, {
      props: {
        modelValue: mockFormData,
        config: inlineConfig,
      },
    })

    expect(wrapper.find('.el-form--inline').exists()).toBe(true)
  })
})
