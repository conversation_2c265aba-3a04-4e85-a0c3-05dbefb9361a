<template>
  <NeConfigProvider :config="configProviderConfig">
    <template v-if="elements && elements.length > 0">
      <NeRenderCore
        v-for="(element, index) in elements"
        :key="element.id || index"
        :nodes="element"
        :config="getRenderConfig(element)"
        @before-render="handleBeforeRender"
        @after-render="handleAfterRender"
      />
    </template>
  </NeConfigProvider>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import NeConfigProvider from '../../config-provider/src/config-provider.vue'
import NeRenderCore from './render-core/render-core.vue'
import { neMaterialRenderProps } from './material-render'

defineOptions({
  name: 'NeMaterialRender',
})

const props = defineProps(neMaterialRenderProps)

const emit = defineEmits(['beforeRender', 'afterRender'])

// ConfigProvider 配置
const configProviderConfig = computed(() => props.config.configProvider || {})

// 获取渲染配置
const getRenderConfig = (element: any) => {
  return {
    ...props.config,
    ...element.config,
  }
}

const handleBeforeRender = (node: any) => {
  emit('beforeRender', node)
}

const handleAfterRender = (vnode: any, node: any) => {
  emit('afterRender', vnode, node)
}
</script>
