export default {
  name: 'eo',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: '<PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON>',
    },
    datepicker: {
      now: 'Nun',
      today: '<PERSON><PERSON><PERSON>',
      cancel: '<PERSON>uli<PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON>',
      confirm: '<PERSON>',
      selectDate: '<PERSON><PERSON><PERSON> daton',
      selectTime: '<PERSON><PERSON><PERSON> horon',
      startDate: 'Komen<PERSON> Da<PERSON>',
      startTime: 'Komenca Horo',
      endDate: '<PERSON><PERSON> Dato',
      endTime: '<PERSON>a Horo',
      prevYear: 'Antaŭa Jaro',
      nextYear: 'Sekva <PERSON>',
      prevMonth: 'Anta<PERSON>a Mona<PERSON>',
      nextMonth: 'Sek<PERSON>',
      year: 'J<PERSON>',
      month1: 'Janua<PERSON>',
      month2: 'Februaro',
      month3: 'Mart<PERSON>',
      month4: 'Aprilo',
      month5: 'Majo',
      month6: 'Jun<PERSON>',
      month7: '<PERSON>',
      month8: 'Aŭgusto',
      month9: 'Septembro',
      month10: 'Okto<PERSON>',
      month11: 'Novem<PERSON>',
      month12: 'Decembro',
      week: '<PERSON><PERSON><PERSON><PERSON>',
      weeks: {
        sun: 'Dim',
        mon: 'Lun',
        tue: 'Mar',
        wed: 'Mer',
        thu: 'Ĵaŭ',
        fri: 'Ven',
        sat: 'Sab',
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Maj',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aŭg',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dec',
      },
    },
    select: {
      loading: 'Ŝarĝante',
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      placeholder: 'Bonvolu elekti',
    },
    mention: {
      loading: 'Ŝarĝante',
    },
    cascader: {
      noMatch: 'Neniuj kongruaj datumoj',
      loading: 'Ŝarĝante',
      placeholder: 'Bonvolu elekti',
      noData: 'Neniuj datumoj',
    },
    pagination: {
      goto: 'Iru al',
      pagesize: '/ paĝo',
      total: 'Entute {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
    },
    messagebox: {
      title: 'Mesaĝo',
      confirm: 'Bone',
      cancel: 'Nuligi',
      error: 'Nevalida Enigo!',
    },
    upload: {
      deleteTip: 'Premu "Delete" por forigi',
      delete: 'Forigi',
      preview: 'Antaŭrigardi',
      continue: 'Daŭrigi',
    },
    table: {
      emptyText: 'Neniuj datumoj',
      confirmFilter: 'Konfirmi',
      resetFilter: 'Restarigi',
      clearFilter: 'Ĉiuj',
      sumText: 'Sumo',
    },
    tree: {
      emptyText: 'Neniuj datumoj',
    },
    transfer: {
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      titles: ['Listo 1', 'Listo 2'],
      filterPlaceholder: 'Enigu ŝlosilvorton',
      noCheckedFormat: '{total} elementoj',
      hasCheckedFormat: '{checked}/{total} elektitaj',
    },
    image: {
      error: 'MALSUKCESIS',
    },
    pageHeader: {
      title: 'Reen',
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No', // to be translated
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
