import type { TableColumnCtx } from 'element-plus'

export type ValueEnumStatus =
  | 'Success'
  | 'Error'
  | 'Processing'
  | 'Default'
  | 'Warning'

export interface ValueEnumItem {
  text: string
  status?: ValueEnumStatus
  color?: string
  disabled?: boolean
}

export type ValueEnum = Record<string | number, ValueEnumItem>

export interface NeTableColumnProps<T = any>
  extends Partial<TableColumnCtx<T>> {
  valueEnum?: ValueEnum
}
