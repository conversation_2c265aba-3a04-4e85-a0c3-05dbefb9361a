import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'
import type { Language } from '@neue-plus/locale'

// 主题配置接口
export interface NeThemeConfig {
  primaryColor?: string
  successColor?: string
  warningColor?: string
  dangerColor?: string
  infoColor?: string
  borderRadius?: string
  fontSize?: string
  fontFamily?: string
}

// 尺寸配置
export type NeSize = 'large' | 'default' | 'small'

// 配置提供者属性
export const neConfigProviderProps = buildProps({
  // 语言配置
  locale: {
    type: Object as () => Language,
  },

  // 全局尺寸
  size: {
    type: String as () => NeSize,
    default: 'default',
  },

  // 主题配置
  theme: {
    type: Object as () => NeThemeConfig,
    default: () => ({}),
  },

  // 是否开启暗黑模式
  dark: {
    type: Boolean,
    default: false,
  },

  // 全局禁用状态
  disabled: {
    type: Boolean,
    default: false,
  },

  // z-index 基础值
  zIndex: {
    type: Number,
    default: 2000,
  },

  // 命名空间前缀
  namespace: {
    type: String,
    default: 'ne',
  },

  // 是否开启严格模式
  strict: {
    type: Boolean,
    default: false,
  },
} as const)

export type NeConfigProviderProps = ExtractPropTypes<
  typeof neConfigProviderProps
>

// 配置上下文
export interface NeConfigContext {
  locale?: Language
  size?: NeSize
  theme?: NeThemeConfig
  dark?: boolean
  disabled?: boolean
  zIndex?: number
  namespace?: string
  strict?: boolean
}
