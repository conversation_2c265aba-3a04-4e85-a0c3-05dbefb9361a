{"name": "@neue-plus/theme-chalk", "version": "0.1.0", "description": "Element component chalk theme.", "keywords": ["vue-components", "neue-plus", "theme-chalk", "theme-light"], "homepage": "https://github.com/neue-plus/neue-plus/blob/dev/packages/theme-chalk/README.md", "bugs": {"url": "https://github.com/neue-plus/neue-plus#issues"}, "license": "MIT", "author": "<EMAIL>", "main": "index.css", "unpkg": "index.css", "jsdelivr": "index.css", "repository": {"type": "git", "url": "git+https://github.com/neue-plus/neue-plus.git"}, "style": "index.css", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "gulp --require tsx/dist/loader.cjs"}, "devDependencies": {"@neue-plus/build": "workspace:*", "@types/gulp-autoprefixer": "^0.0.33", "@types/gulp-rename": "^2.0.1", "@types/gulp-sass": "^5.0.0", "cssnano": "^6.0.5", "gulp-autoprefixer": "^8.0.0", "gulp-rename": "^2.0.0", "gulp-sass": "^6.0.0", "postcss": "^8.4.35", "tsx": "^4.19.3"}, "gitHead": "c69724230befa8fede0e6b9c37fb0b7e39fd7cdd"}