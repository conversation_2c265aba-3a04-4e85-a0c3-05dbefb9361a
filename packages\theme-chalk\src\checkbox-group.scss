@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: getCssVar('spacing-md', 12px);
  align-items: flex-start;

  // 垂直布局
  @include m(vertical) {
    flex-direction: column;
    align-items: flex-start;
    gap: getCssVar('spacing-sm', 8px);

    .#{$namespace}-checkbox {
      margin-right: 0;
      margin-bottom: getCssVar('spacing-sm', 8px);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 带边框样式
  @include m(bordered) {
    .#{$namespace}-checkbox {
      border: 1px solid getCssVar('border-color-light');
      border-radius: getCssVar('border-radius-base');
      padding: getCssVar('spacing-sm', 8px) getCssVar('spacing-md', 12px);
      margin-right: 0;
      margin-bottom: getCssVar('spacing-sm', 8px);
      transition: getCssVar('transition-border');

      &:hover {
        border-color: getCssVar('color-primary');
      }

      &.is-checked {
        border-color: getCssVar('color-primary');
        background-color: getCssVar('color-primary-light-9');
      }

      &.is-indeterminate {
        border-color: getCssVar('color-primary');
        background-color: getCssVar('color-primary-light-8');
      }

      &.is-disabled {
        border-color: getCssVar('border-color-lighter');
        background-color: getCssVar('fill-color-light');
        cursor: not-allowed;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 带全选功能
  @include m(with-select-all) {
    flex-direction: column;
    align-items: flex-start;
    gap: getCssVar('spacing-md', 12px);

    .#{$namespace}-checkbox-group__select-all {
      padding-bottom: getCssVar('spacing-sm', 8px);
      border-bottom: 1px solid getCssVar('border-color-lighter');
      margin-bottom: getCssVar('spacing-sm', 8px);
      width: 100%;

      .#{$namespace}-checkbox {
        font-weight: 500;
        color: getCssVar('text-color-primary');
      }
    }

    @include e(inner) {
      display: flex;
      flex-wrap: wrap;
      gap: inherit;
      width: 100%;

      &.#{$namespace}-checkbox-group__inner--vertical {
        flex-direction: column;
        gap: getCssVar('spacing-sm', 8px);
      }
    }
  }

  // 内部复选框组样式
  @include e(inner) {
    display: flex;
    flex-wrap: wrap;
    gap: inherit;

    @include m(vertical) {
      flex-direction: column;
      align-items: flex-start;
      gap: getCssVar('spacing-sm', 8px);
    }
  }

  // 尺寸变体
  @include m(large) {
    gap: getCssVar('spacing-lg', 16px);

    .#{$namespace}-checkbox {
      font-size: getCssVar('font-size-large');
    }

    &.#{$namespace}-checkbox-group--vertical {
      gap: getCssVar('spacing-md', 12px);
    }
  }

  @include m(small) {
    gap: getCssVar('spacing-sm', 8px);

    .#{$namespace}-checkbox {
      font-size: getCssVar('font-size-small');
    }

    &.#{$namespace}-checkbox-group--vertical {
      gap: getCssVar('spacing-xs', 4px);
    }
  }

  // 禁用状态
  @include when(disabled) {
    .#{$namespace}-checkbox {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  // 内部复选框样式调整
  .#{$namespace}-checkbox {
    margin-right: 0;
    white-space: nowrap;

    &:focus-visible {
      outline: 2px solid getCssVar('color-primary');
      outline-offset: 2px;
      border-radius: getCssVar('border-radius-small');
    }

    // 复选框标签
    .#{$namespace}-checkbox__label {
      font-size: inherit;
      color: getCssVar('text-color-regular');
      transition: getCssVar('transition-color');
    }

    // 选中状态
    &.is-checked {
      .#{$namespace}-checkbox__label {
        color: getCssVar('color-primary');
        font-weight: 500;
      }
    }

    // 半选状态
    &.is-indeterminate {
      .#{$namespace}-checkbox__label {
        color: getCssVar('color-primary');
        font-weight: 500;
      }
    }

    // 禁用状态
    &.is-disabled {
      .#{$namespace}-checkbox__label {
        color: getCssVar('text-color-disabled');
      }
    }
  }

  // 响应式设计
  @media (max-width: getCssVar('breakpoint-sm', 576px)) {
    &:not(.#{$namespace}-checkbox-group--vertical) {
      flex-direction: column;
      align-items: flex-start;
      gap: getCssVar('spacing-sm', 8px);
    }
  }

  // 暗黑模式适配
  @at-root {
    .dark & {
      .#{$namespace}-checkbox {
        .#{$namespace}-checkbox__label {
          color: getCssVar('text-color-primary');
        }

        &.is-checked,
        &.is-indeterminate {
          .#{$namespace}-checkbox__label {
            color: getCssVar('color-primary');
          }
        }

        &.is-disabled {
          .#{$namespace}-checkbox__label {
            color: getCssVar('text-color-disabled');
          }
        }
      }

      &.#{$namespace}-checkbox-group--bordered {
        .#{$namespace}-checkbox {
          border-color: getCssVar('border-color');
          background-color: transparent;

          &:hover {
            border-color: getCssVar('color-primary');
          }

          &.is-checked {
            border-color: getCssVar('color-primary');
            background-color: rgba(getCssVar('color-primary-rgb'), 0.1);
          }

          &.is-indeterminate {
            border-color: getCssVar('color-primary');
            background-color: rgba(getCssVar('color-primary-rgb'), 0.15);
          }

          &.is-disabled {
            border-color: getCssVar('border-color-light');
            background-color: getCssVar('fill-color');
          }
        }
      }

      &.#{$namespace}-checkbox-group--with-select-all {
        .#{$namespace}-checkbox-group__select-all {
          border-bottom-color: getCssVar('border-color');
        }
      }
    }
  }

  // 动画效果
  .#{$namespace}-checkbox {
    transition: all getCssVar('transition-duration-fast') getCssVar('transition-function-ease-in-out-bezier');

    &:hover:not(.is-disabled) {
      transform: translateY(-1px);
    }

    &:active:not(.is-disabled) {
      transform: translateY(0);
    }
  }

  // 自定义间距支持
  &[data-gap] {
    gap: var(--ne-checkbox-group-gap);
  }

  // 内联样式支持
  &.#{$namespace}-checkbox-group--inline {
    display: inline-flex;
    vertical-align: middle;
  }

  // 全选按钮特殊样式
  .#{$namespace}-checkbox--select-all {
    .#{$namespace}-checkbox__input {
      .#{$namespace}-checkbox__inner {
        border-width: 2px;
      }
    }

    .#{$namespace}-checkbox__label {
      font-weight: 600;
    }
  }
}
