@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(table) {
  @include set-component-css-var('table', $table);

  // 自定义bordered样式
  @include when(bordered) {
    .el-table {
      border: 1px solid getCssVar('border-color');
      border-radius: getCssVar('border-radius-base');

      .el-table__header th,
      .el-table__body td {
        border-right: 1px solid getCssVar('border-color');
      }

      .el-table__header th:last-child,
      .el-table__body td:last-child {
        border-right: none;
      }
    }
  }
}
