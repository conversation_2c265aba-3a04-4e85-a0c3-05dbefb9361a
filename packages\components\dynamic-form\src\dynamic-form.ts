import NeCheckboxGroup from '@neue-plus/components/checkbox-group'
import NeRadioGroup from '@neue-plus/components/radio-group'
import { buildProp, buildProps } from '@neue-plus/utils'
import type { FormItemProps, FormProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

// 表单项类型
export type DynamicFormItemType =
  | 'input'
  | 'textarea'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'switch'
  | 'date'
  | 'daterange'
  | 'time'
  | 'number'
  | 'password'
  | 'upload'
  | 'cascader'
  | 'tree-select'

// 选项配置
export interface DynamicFormOption {
  label: string
  value: any
  disabled?: boolean
  children?: DynamicFormOption[]
}

// 表单项配置
export interface DynamicFormItem extends Partial<FormItemProps> {
  // 基础配置
  prop: string
  label: string
  type: DynamicFormItemType

  // 显示控制
  show?: boolean
  disabled?: boolean

  // 表单项属性
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  filterable?: boolean

  // 选项配置
  options?: DynamicFormOption[]

  // 验证规则
  required?: boolean
  rules?: any[]

  // 输入框配置
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean

  // 数字输入框配置
  min?: number
  max?: number
  step?: number
  precision?: number

  // 日期配置
  format?: string
  valueFormat?: string

  // 上传配置
  action?: string
  accept?: string
  limit?: number

  // 级联选择器配置
  props?: any

  // 自定义渲染
  render?: (formData: Record<string, any>) => any

  // 插槽名称
  slot?: string

  // 栅格布局
  span?: number
  offset?: number

  // 其他属性
  [key: string]: any
}

// 表单配置
export interface DynamicFormConfig {
  // 表单项配置
  items: DynamicFormItem[]

  // 表单布局
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'

  // 栅格配置
  gutter?: number

  // 提交按钮配置
  showSubmit?: boolean
  submitText?: string
  showReset?: boolean
  resetText?: string
  showCancel?: boolean
  cancelText?: string

  // 按钮位置
  buttonAlign?: 'left' | 'center' | 'right'
}

export const dynamicFormProps = buildProps({
  // 表单数据
  modelValue: buildProp({
    type: Object as () => Record<string, any>,
    default: () => ({}),
  }),

  // 表单配置
  config: buildProp({
    type: Object as () => DynamicFormConfig,
    required: true,
  }),

  // 是否加载中
  loading: buildProp({
    type: Boolean,
    default: false,
  }),

  // 是否禁用
  disabled: buildProp({
    type: Boolean,
    default: false,
  }),

  // 表单大小
  size: buildProp({
    type: String,
    values: ['large', 'default', 'small'],
    default: 'default',
  }),
} as const)

export type DynamicFormProps = ExtractPropTypes<typeof dynamicFormProps> &
  Omit<FormProps, 'model'>

// 事件类型
export type DynamicFormEmits = {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'submit', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'cancel'): void
  (e: 'change', prop: string, value: any): void
  (e: 'validate', prop: string, isValid: boolean, message: string): void
}

export interface ComponentMap {
  input: string
  textarea: string
  select: string
  radio: typeof NeRadioGroup
  checkbox: typeof NeCheckboxGroup
  switch: string
  date: string
  daterange: string
  time: string
  number: string
  password: string
  upload: string
  cascader: string
  'tree-select': string
}
