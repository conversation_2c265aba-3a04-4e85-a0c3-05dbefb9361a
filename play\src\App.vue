<template>
  <div>
    <NeCard>
      <h3>Card组件测试</h3>
      <p>这是一个基于Element Plus封装的Card组件</p>
    </NeCard>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Slot方式</h3>
      <NeTable :data="tableData">
        <el-table-column prop="name" label="姓名" width="180" />
        <el-table-column prop="age" label="年龄" width="180" />
        <el-table-column prop="address" label="地址" />
      </NeTable>
    </div>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Columns配置</h3>
      <NeTable :data="tableData" :columns="tableColumns" />
    </div>

    <div class="mt-4">
      <h3>ProTable组件测试 - 包含筛选和分页</h3>
      <NeProTable
        :data="currentPageData"
        :columns="proTableColumns"
        :loading="loading"
        :pagination="pagination"
        @filter-change="handleFilterChange"
        @page-change="handlePageChange"
        @sort-change="handleSortChange"
        @refresh="handleRefresh"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import type {
  ProTableColumn,
  ProTableFilters,
} from '@neue-plus/components/pro-table'

const tableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
  },
])

// 表格列配置 - 继承el-table-column的所有属性
const tableColumns = ref([
  {
    prop: 'name',
    label: '姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    sortable: true,
  },
  {
    prop: 'address',
    label: '地址',
  },
])

// ProTable相关数据
const loading = ref(false)
const filters = ref<ProTableFilters>({})
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 扩展数据用于ProTable测试
const allProTableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    status: 'active',
    createTime: '2024-01-01',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    status: 'inactive',
    createTime: '2024-01-02',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
    status: 'active',
    createTime: '2024-01-03',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
    status: 'pending',
    createTime: '2024-01-04',
  },
  {
    name: '钱七',
    age: 26,
    address: '杭州市西湖区',
    status: 'active',
    createTime: '2024-01-05',
  },
  {
    name: '孙八',
    age: 29,
    address: '南京市鼓楼区',
    status: 'inactive',
    createTime: '2024-01-06',
  },
  {
    name: '周九',
    age: 31,
    address: '武汉市洪山区',
    status: 'active',
    createTime: '2024-01-07',
  },
  {
    name: '吴十',
    age: 27,
    address: '成都市锦江区',
    status: 'pending',
    createTime: '2024-01-08',
  },
])

// ProTable列配置 - 包含筛选功能
const proTableColumns = ref<ProTableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
    filterable: true,
    filterType: 'input',
    filterPlaceholder: '请输入姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 100,
    sortable: true,
    align: 'center',
  },
  {
    prop: 'status',
    label: '状态',
    width: 120,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '激活', value: 'active' },
      { label: '未激活', value: 'inactive' },
      { label: '待审核', value: 'pending' },
    ],
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 150,
    filterable: true,
    filterType: 'date',
  },
  {
    prop: 'address',
    label: '地址',
    showOverflowTooltip: true,
    minWidth: 200,
  },
])

// 计算当前页数据
const currentPageData = computed(() => {
  let filteredData = allProTableData.value

  // 应用筛选
  if (filters.value.name) {
    filteredData = filteredData.filter((item) =>
      item.name.includes(filters.value.name)
    )
  }
  if (filters.value.status) {
    filteredData = filteredData.filter(
      (item) => item.status === filters.value.status
    )
  }
  if (filters.value.createTime) {
    filteredData = filteredData.filter((item) =>
      item.createTime.includes(filters.value.createTime)
    )
  }

  // 更新总数
  pagination.value.total = filteredData.length

  // 分页
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.slice(start, end)
})

// 事件处理方法
const handleFilterChange = (newFilters: ProTableFilters) => {
  filters.value = newFilters
  pagination.value.current = 1 // 重置到第一页
  console.log('筛选变化:', newFilters)
}

const handlePageChange = (current: number, pageSize: number) => {
  pagination.value.current = current
  pagination.value.pageSize = pageSize
  console.log('分页变化:', { current, pageSize })
}

const handleSortChange = (
  prop: string,
  order: 'ascending' | 'descending' | null
) => {
  console.log('排序变化:', { prop, order })
  // 这里可以实现排序逻辑
}

const handleRefresh = () => {
  loading.value = true
  console.log('刷新数据')
  // 模拟刷新
  setTimeout(() => {
    loading.value = false
  }, 1000)
}
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
