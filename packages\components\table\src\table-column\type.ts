import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

export type ValueEnumStatus =
  | 'Success'
  | 'Error'
  | 'Processing'
  | 'Default'
  | 'Warning'

export interface ValueEnumItem {
  text: string
  status?: ValueEnumStatus
  color?: string
  disabled?: boolean
}

export type ValueEnum = Record<string | number, ValueEnumItem>

// 继承Element Plus的TableColumnCtx，只添加自定义属性
export const neTableColumnProps = buildProps({
  // 自定义属性：值枚举
  valueEnum: {
    type: Object as () => ValueEnum,
  },
} as const)

export type NeTableColumnProps = ExtractPropTypes<typeof neTableColumnProps> &
  Partial<TableColumnCtx<any>>
