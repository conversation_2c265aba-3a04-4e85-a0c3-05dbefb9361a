export const initialData = [
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
]
export const tableData = [
  {
    id: 1,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 2,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now() - 24 * 60 * 60 * 1000,
    date: '2016-05-04',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 3,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
    children: [
      {
        id: 31,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        id: 32,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
  {
    id: 4,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
]
