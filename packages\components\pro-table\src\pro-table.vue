<template>
  <div class="ne-pro-table">
    <!-- 筛选表单 -->
    <div v-if="showFilter && filterColumns.length > 0" class="ne-pro-table__filter">
      <el-form :model="filters" inline>
        <el-form-item
          v-for="column in filterColumns"
          :key="column.prop"
          :label="column.label"
        >
          <!-- 输入框筛选 -->
          <el-input
            v-if="column.filterType === 'input'"
            v-model="filters[column.prop!]"
            :placeholder="column.filterPlaceholder || `请输入${column.label}`"
            clearable
            @change="handleFilterChange"
          />
          <!-- 选择器筛选 -->
          <el-select
            v-else-if="column.filterType === 'select'"
            v-model="filters[column.prop!]"
            :placeholder="column.filterPlaceholder || `请选择${column.label}`"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in column.filterOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <!-- 日期筛选 -->
          <el-date-picker
            v-else-if="column.filterType === 'date'"
            v-model="filters[column.prop!]"
            type="date"
            :placeholder="column.filterPlaceholder || `请选择${column.label}`"
            clearable
            @change="handleFilterChange"
          />
          <!-- 日期范围筛选 -->
          <el-date-picker
            v-else-if="column.filterType === 'daterange'"
            v-model="filters[column.prop!]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
            @change="handleFilterChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleRefresh">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <el-table
        v-bind="$attrs"
        :data="data"
        :loading="loading"
        :size="size"
        :class="{ 'ne-table--bordered': bordered }"
        @sort-change="handleSortChange"
      >
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop || column.label"
          v-bind="column"
        />
        <slot />
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination" class="ne-pro-table__pagination">
      <el-pagination
        v-bind="pagination"
        :current-page="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { proTableProps, type ProTableFilters, type ProTableEmits } from './pro-table'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})

const props = defineProps(proTableProps)
const emit = defineEmits<ProTableEmits>()

// 筛选数据
const filters = ref<ProTableFilters>({})

// 可筛选的列
const filterColumns = computed(() => {
  return props.columns.filter(column => column.filterable && !column.hide)
})

// 可见的列
const visibleColumns = computed(() => {
  return props.columns.filter(column => !column.hide)
})

// 初始化筛选表单
const initFilters = () => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach(column => {
    if (column.prop) {
      newFilters[column.prop] = column.filterType === 'daterange' ? [] : ''
    }
  })
  filters.value = newFilters
}

// 筛选变化处理
const handleFilterChange = () => {
  emit('filter-change', filters.value)
}

// 搜索
const handleSearch = () => {
  emit('filter-change', filters.value)
}

// 重置筛选
const handleReset = () => {
  initFilters()
  emit('filter-change', filters.value)
}

// 刷新
const handleRefresh = () => {
  emit('refresh')
}

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  emit('page-change', 1, pageSize)
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  emit('page-change', current, props.pagination.pageSize || 10)
}

// 排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending' | 'descending' | null }) => {
  emit('sort-change', prop, order)
}

// 监听columns变化，重新初始化筛选表单
watch(() => props.columns, () => {
  initFilters()
}, { immediate: true })
</script>

<style scoped>
.ne-pro-table__filter {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.ne-pro-table__pagination {
  margin-top: 16px;
  text-align: right;
}
</style>
