export default {
  name: 'no',
  el: {
    breadcrumb: {
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    },
    colorpicker: {
      confirm: 'Bekreft',
      clear: 'Tøm',
      defaultLabel: 'Fargevelger',
      description: 'Nåværende farge {color}, velg ny farge med Enter-tasten',
      alphaLabel: 'Velg verdi for gjennomsiktighet',
    },
    datepicker: {
      now: 'Nå',
      today: 'I dag',
      cancel: 'Avbryt',
      clear: 'Tøm',
      confirm: 'Bekreft',
      dateTablePrompt: 'Bruk piltastene og Enter-tasten for å velge dato',
      monthTablePrompt: 'Bruk piltastene og Enter-tasten for å velge måned',
      yearTablePrompt: 'Bruk piltastene og Enter-tasten for å velge år',
      selectedDate: 'Valgt dato',
      selectDate: 'Velg dato',
      selectTime: 'Velg tid',
      startDate: 'Startdato',
      startTime: 'Starttid',
      endDate: 'Sluttdato',
      endTime: 'Sluttid',
      prevYear: '<PERSON>rige år',
      nextYear: 'Neste år',
      prevMonth: 'Forrige måned',
      nextMonth: 'Neste måned',
      year: 'År',
      month1: 'Januar',
      month2: 'Februar',
      month3: 'Mars',
      month4: 'April',
      month5: 'Mai',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'August',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'Desember',
      weeks: {
        sun: 'Søn',
        mon: 'Man',
        tue: 'Tir',
        wed: 'Ons',
        thu: 'Tor',
        fri: 'Fre',
        sat: 'Lør',
      },
      weeksFull: {
        sun: 'Søndag',
        mon: 'Mandag',
        tue: 'Tirsdag',
        wed: 'Onsdag',
        thu: 'Torsdag',
        fri: 'Fredag',
        sat: 'Lørdag',
      },
      months: {
        jan: 'Januar',
        feb: 'Februar',
        mar: 'Mars',
        apr: 'April',
        may: 'Mai',
        jun: 'Juni',
        jul: 'Juli',
        aug: 'August',
        sep: 'September',
        oct: 'Oktober',
        nov: 'November',
        dec: 'Desember',
      },
    },
    inputNumber: {
      decrease: 'Minsk verdi',
      increase: 'Øk verdi',
    },
    select: {
      loading: 'Laster',
      noMatch: 'Ingen treff',
      noData: 'Ingen data',
      placeholder: 'Velg',
    },
    dropdown: {
      toggleDropdown: 'Vis/skjul nedtrekksmeny',
    },
    mention: {
      loading: 'Laster',
    },
    cascader: {
      noMatch: 'Ingen treff',
      loading: 'Laster',
      placeholder: 'Velg',
      noData: 'Ingen data',
    },
    pagination: {
      goto: 'Gå til',
      pagesize: 'per side',
      total: 'Totalt {total} elementer',
      pageClassifier: 'side',
      page: 'Side',
      prev: 'Forrige side',
      next: 'Neste side',
      currentPage: 'Side {pager}',
      prevPages: 'Forrige {pager} sider',
      nextPages: 'Neste {pager} sider',
      deprecationWarning:
        'Du bruker noen foreldede metoder, se den offisielle dokumentasjonen for el-pagination',
    },
    dialog: {
      close: 'Lukk denne dialogboksen',
    },
    drawer: {
      close: 'Lukk denne dialogboksen',
    },
    messagebox: {
      title: 'Varsel',
      confirm: 'Bekreft',
      cancel: 'Avbryt',
      error: 'Ugyldig inndata!',
      close: 'Lukk denne dialogboksen',
    },
    upload: {
      deleteTip: 'Trykk delete for å slette',
      delete: 'Slett',
      preview: 'Vis bilde',
      continue: 'Fortsett opplasting',
    },
    slider: {
      defaultLabel: 'Glidebryter mellom {min} og {max}',
      defaultRangeStartLabel: 'Velg startverdi',
      defaultRangeEndLabel: 'Velg sluttverdi',
    },
    table: {
      emptyText: 'Ingen data',
      confirmFilter: 'Filtrer',
      resetFilter: 'Tilbakestill',
      clearFilter: 'Alle',
      sumText: 'Sum',
    },
    tour: {
      next: 'Neste',
      previous: 'Forrige',
      finish: 'Avslutt omvisning',
    },
    tree: {
      emptyText: 'Ingen data',
    },
    transfer: {
      noMatch: 'Ingen treff',
      noData: 'Ingen data',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Skriv inn søkeinnhold',
      noCheckedFormat: 'Totalt {total} elementer',
      hasCheckedFormat: 'Valgt {checked}/{total} elementer',
    },
    image: {
      error: 'Lasting mislyktes',
    },
    pageHeader: {
      title: 'Tilbake',
    },
    popconfirm: {
      confirmButtonText: 'Bekreft',
      cancelButtonText: 'Avbryt',
    },
    carousel: {
      leftArrow: 'Forrige bilde',
      rightArrow: 'Neste bilde',
      indicator: 'Bytt bilde til indeks {index}',
    },
  },
}
