export default {
  name: 'bn',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: 'ঠিক আছে',
      clear: 'ক্লিয়ার',
    },
    datepicker: {
      now: 'এখন',
      today: 'আজ',
      cancel: 'বাতিল',
      clear: 'ক্লিয়ার',
      confirm: 'ঠিক আছে',
      selectDate: 'তারিখ নির্বাচন করুন',
      selectTime: 'সময় নির্বাচন করুন',
      startDate: 'যে তারিখ থেকে',
      startTime: 'যে সময় থেকে',
      endDate: 'যে তারিখ পর্যন্ত',
      endTime: 'যে সময় পর্যন্ত',
      prevYear: 'পূর্ববর্তী বছর',
      nextYear: 'পরবর্তী বছর',
      prevMonth: 'পূর্ববর্তী মাস',
      nextMonth: 'পরবর্তী মাস',
      year: 'সাল',
      month1: 'জানুয়ারি',
      month2: 'ফেব্রুয়ারী',
      month3: 'মার্চ',
      month4: 'এপ্রিল',
      month5: 'মে',
      month6: 'জুন',
      month7: 'জুলাই',
      month8: 'আগষ্ট',
      month9: 'সেপ্টেম্বর',
      month10: 'অক্টোবর',
      month11: 'নভেম্বর',
      month12: 'ডিসেম্বর',
      week: 'সাপ্তাহ',
      weeks: {
        sun: 'রবি',
        mon: 'সোম',
        tue: 'মঙ্গল',
        wed: 'বুধ',
        thu: 'বৃহঃ',
        fri: 'শুক্র',
        sat: 'শনি',
      },
      months: {
        jan: 'জানু',
        feb: 'ফেব্রু',
        mar: 'মার্চ',
        apr: 'এপ্রি',
        may: 'মে',
        jun: 'জুন',
        jul: 'জুলা',
        aug: 'আগ',
        sep: 'সেপ্টে',
        oct: 'আক্টো',
        nov: 'নভে',
        dec: 'ডিসে',
      },
    },
    select: {
      loading: 'লোড হচ্ছে',
      noMatch: 'কোন মিল পওয়া যায়নি',
      noData: 'কোন ডাটা নেই',
      placeholder: 'নির্বাচন করুন',
    },
    mention: {
      loading: 'লোড হচ্ছে',
    },
    cascader: {
      noMatch: 'কোন মিল পওয়া যায়নি',
      loading: 'লোড হচ্ছে',
      placeholder: 'নির্বাচন করুন',
      noData: 'কোন ডাটা নেই',
    },
    pagination: {
      goto: 'যান',
      pagesize: '/পেজ',
      total: 'মোট {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
      deprecationWarning:
        'অপ্রচলিত (Deprecated) ব্যাবহার পওয়া গেছে, আরও জানতে চাইলে, দয়া করে el-pagination এর ডকুমেন্টেশন দেখুন',
    },
    messagebox: {
      title: 'বার্তা',
      confirm: 'ঠিক আছে',
      cancel: 'বাতিল',
      error: 'ইনপুট ডাটা গ্রহনযোগ্য নয়',
    },
    upload: {
      deleteTip: 'অপসারণ করতে "ডিলিট" এ ক্লিক করুন',
      delete: 'ডিলিট',
      preview: 'প্রিভিউ',
      continue: 'চালিয়ে যান',
    },
    table: {
      emptyText: 'কোন ডাটা নেই',
      confirmFilter: 'নিশ্চিত করুন',
      resetFilter: 'রিসেট',
      clearFilter: 'সব',
      sumText: 'সারাংশ',
    },
    tree: {
      emptyText: 'কোন ডাটা নেই',
    },
    transfer: {
      noMatch: 'কোন মিল পওয়া যায়নি',
      noData: 'কোন ডাটা নেই',
      titles: ['লিস্ট ১', 'লিস্ট ২'],
      filterPlaceholder: 'সার্চ করুন',
      noCheckedFormat: '{total} আইটেম',
      hasCheckedFormat: '{checked}/{total} টিক করা হয়েছে',
    },
    image: {
      error: 'ব্যর্থ হয়েছে',
    },
    pageHeader: {
      title: 'পিছনে',
    },
    popconfirm: {
      confirmButtonText: 'হ্যা',
      cancelButtonText: 'না',
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
