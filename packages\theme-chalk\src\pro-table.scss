@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(pro-table) {
  @include e(filter) {
    margin-bottom: 16px;
    padding: 16px;
    background-color: getCssVar('fill-color', 'lighter');
    border-radius: getCssVar('border-radius-base');
    border: 1px solid getCssVar('border-color', 'lighter');

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }

    .el-form-item__label {
      font-weight: 500;
      color: getCssVar('text-color', 'primary');
    }
  }

  @include e(table) {
    .el-table {
      border-radius: getCssVar('border-radius-base');
    }
  }

  @include e(pagination) {
    margin-top: 16px;
    text-align: right;
    padding: 16px 0;
    border-top: 1px solid getCssVar('border-color', 'lighter');

    .el-pagination {
      justify-content: flex-end;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    @include e(filter) {
      .el-form {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    @include e(pagination) {
      text-align: center;

      .el-pagination {
        justify-content: center;
      }
    }
  }
}
