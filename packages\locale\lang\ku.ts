export default {
  name: 'ku',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: '<PERSON>ma<PERSON>',
      clear: 'Paqij bike',
    },
    datepicker: {
      now: '<PERSON>ha',
      today: 'Îro',
      cancel: 'Betal bike',
      clear: '<PERSON>qij bike',
      confirm: 'Tema<PERSON>',
      selectDate: 'Dîrokê bibijêre',
      selectTime: 'Demê bibijêre',
      startDate: 'Dîrok<PERSON> Destpêkê',
      startTime: '<PERSON><PERSON>tpêkê',
      endDate: 'Dîroka Dawî',
      endTime: 'Dema Dawî',
      prevYear: 'Sala <PERSON>',
      nextYear: '<PERSON>a <PERSON>',
      prevMonth: '<PERSON>ha <PERSON>',
      nextMonth: '<PERSON><PERSON>',
      year: 'Sal',
      month1: 'Rêbendan',
      month2: 'Reşemeh',
      month3: 'Adar',
      month4: 'Avrêl',
      month5: 'Gulan',
      month6: '<PERSON><PERSON><PERSON><PERSON>',
      month7: 'T<PERSON><PERSON><PERSON>',
      month8: '<PERSON>av<PERSON><PERSON>',
      month9: '<PERSON><PERSON><PERSON>',
      month10: 'Kew<PERSON>êr',
      month11: 'Sarmawaz',
      month12: 'Be<PERSON><PERSON>bar',
      // week: 'week',
      weeks: {
        sun: 'Yek',
        mon: 'Duş',
        tue: 'Sêş',
        wed: 'Çar',
        thu: 'Pên',
        fri: 'În',
        sat: 'Şem',
      },
      months: {
        jan: 'Rêb',
        feb: 'Reş',
        mar: 'Ada',
        apr: 'Avr',
        may: 'Gul',
        jun: 'Pûş',
        jul: 'Tîr',
        aug: 'Gil',
        sep: 'Rez',
        oct: 'Kew',
        nov: 'Sar',
        dec: 'Ber',
      },
    },
    select: {
      loading: 'Bardibe',
      noMatch: 'Li hembere ve agahî tune',
      noData: 'Agahî tune',
      placeholder: 'Bibijêre',
    },
    mention: {
      loading: 'Bardibe',
    },
    cascader: {
      noMatch: 'Li hembere ve agahî tune',
      loading: 'Bardibe',
      placeholder: 'Bibijêre',
      noData: 'Agahî tune',
    },
    pagination: {
      goto: 'Biçe',
      pagesize: '/rupel',
      total: 'Tevahî {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
    },
    messagebox: {
      title: 'Peyam',
      confirm: 'Temam',
      cancel: 'Betal bike',
      error: 'Beyana çewt',
    },
    upload: {
      deleteTip: 'ji bo rake pêl "delete" bike',
      delete: 'Rake',
      preview: 'Pêşdîtin',
      continue: 'Berdewam',
    },
    table: {
      emptyText: 'Agahî tune',
      confirmFilter: 'Piştrast bike',
      resetFilter: 'Jê bibe',
      clearFilter: 'Hemû',
      sumText: 'Kom',
    },
    tree: {
      emptyText: 'Agahî tune',
    },
    transfer: {
      noMatch: 'Li hembere ve agahî tune',
      noData: 'Agahî tune',
      titles: ['Lîste 1', 'Lîste 2'],
      filterPlaceholder: 'Binivîse',
      noCheckedFormat: '{total} lib',
      hasCheckedFormat: '{checked}/{total} bijartin',
    },
    image: {
      error: 'FAILED', // to be translated
    },
    pageHeader: {
      title: 'Back', // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No', // to be translated
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
