import { radioGroupProps, radioProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

// 选项配置接口
export const neRadioProps = buildProps({
  ...radioProps,
} as const)
export type NeRadioProps = ExtractPropTypes<typeof neRadioProps>
// 继承 Element Plus RadioGroup 的所有属性，并添加自定义属性
export const neRadioGroupProps = buildProps({
  // 继承 Element Plus RadioGroup 的所有属性
  ...radioGroupProps,
  // 自定义属性：选项配置数组
  options: {
    type: Array as () => Array<NeRadioProps>,
    default: () => [],
  },
} as const)

export type NeRadioGroupProps = ExtractPropTypes<typeof neRadioGroupProps>

// 事件类型定义
export type NeRadioGroupEmits = {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}
