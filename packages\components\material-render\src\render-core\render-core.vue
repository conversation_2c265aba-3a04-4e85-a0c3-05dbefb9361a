<template>
  <component
    :is="resolvedComponent || config.type"
    :ref="setRef"
    v-bind="elProps"
    v-on="eventHandlers"
  >
    <!-- 默认插槽 -->
    <template v-if="config.elements?.length" #default>
      <NeRenderCore
        v-for="(child, index) in config.elements"
        :key="child.id || index"
        :config="child"
      />
    </template>

    <!-- 动态插槽 -->
    <template
      v-for="(content, slotName) in config.slots"
      :key="slotName"
      #[slotName]
    >
      <!-- 数组类型的插槽内容 -->
      <template v-if="Array.isArray(content)">
        <NeRenderCore
          v-for="(child, index) in content"
          :key="child.id || index"
          :config="child"
        />
      </template>
      <!-- 简单文本插槽内容 -->
      <template v-else>
        {{ content }}
      </template>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, inject } from 'vue'
import { getComponentByName } from '../utils'
import { getComponentEvent, useElementProps } from '../hooks'
import { neMaterialRenderProps } from '../material-render'
import type { SchemaRefs } from '../hooks'

// 异步加载防止循环引用
const NeRenderCore = defineAsyncComponent(() => import('./render-core.vue'))

defineOptions({
  name: 'NeRenderCore',
})

const props = defineProps(neMaterialRenderProps)

// 解析组件
const resolvedComponent = computed(() => getComponentByName(props.config.type))

// 处理元素属性
const { elProps } = useElementProps(props.config)

// 获取 refs 上下文
const refs: SchemaRefs = inject('refs') || {}

// 处理事件
const eventHandlers = getComponentEvent(props.config, refs)

// 设置组件引用
const setRef = (el: any) => {
  if (refs && props.config.id) {
    refs[props.config.id] = el
  }
}
</script>
