export default {
  name: 'tr',
  el: {
    breadcrumb: {
      label: 'Breadcrumb', // to be translated
    },
    colorpicker: {
      confirm: '<PERSON><PERSON><PERSON>',
      clear: '<PERSON><PERSON><PERSON>',
    },
    datepicker: {
      now: '<PERSON><PERSON><PERSON>',
      today: '<PERSON><PERSON><PERSON><PERSON>',
      cancel: '<PERSON>pta<PERSON>',
      clear: 'Temizle',
      confirm: '<PERSON><PERSON><PERSON>',
      selectDate: '<PERSON><PERSON><PERSON> seç',
      selectTime: 'Saat seç',
      startDate: 'Başlangıç Tarihi',
      startTime: '<PERSON>şlangıç Saati',
      endDate: 'Bitiş Tarihi',
      endTime: '<PERSON><PERSON><PERSON> Saati',
      prevYear: 'Önceki Yıl',
      nextYear: '<PERSON><PERSON><PERSON> Yıl',
      prevMonth: 'Önceki Ay',
      nextMonth: '<PERSON>rak<PERSON> Ay',
      year: '',
      month1: 'Ocak',
      month2: 'Şubat',
      month3: 'Mart',
      month4: 'Nisan',
      month5: 'Mayıs',
      month6: 'Haziran',
      month7: 'Temmuz',
      month8: 'Ağust<PERSON>',
      month9: '<PERSON><PERSON><PERSON><PERSON>',
      month10: '<PERSON><PERSON>',
      month11: 'Kasım',
      month12: 'Ara<PERSON><PERSON><PERSON>',
      // week: 'week',
      weeks: {
        sun: 'Paz',
        mon: 'Pzt',
        tue: 'Sal',
        wed: 'Çar',
        thu: 'Per',
        fri: 'Cum',
        sat: 'Cmt',
      },
      months: {
        jan: 'Oca',
        feb: 'Şub',
        mar: 'Mar',
        apr: 'Nis',
        may: 'May',
        jun: 'Haz',
        jul: 'Tem',
        aug: 'Ağu',
        sep: 'Eyl',
        oct: 'Eki',
        nov: 'Kas',
        dec: 'Ara',
      },
    },
    select: {
      loading: 'Yükleniyor',
      noMatch: 'Eşleşen veri bulunamadı',
      noData: 'Veri yok',
      placeholder: 'Seç',
    },
    mention: {
      loading: 'Yükleniyor',
    },
    cascader: {
      noMatch: 'Eşleşen veri bulunamadı',
      loading: 'Yükleniyor',
      placeholder: 'Seç',
      noData: 'Veri yok',
    },
    pagination: {
      goto: 'Git',
      pagesize: '/sayfa',
      total: 'Toplam {total}',
      pageClassifier: '',
      page: 'Page', // to be translated
      prev: 'Go to previous page', // to be translated
      next: 'Go to next page', // to be translated
      currentPage: 'page {pager}', // to be translated
      prevPages: 'Previous {pager} pages', // to be translated
      nextPages: 'Next {pager} pages', // to be translated
    },
    messagebox: {
      title: 'Mesaj',
      confirm: 'Onayla',
      cancel: 'İptal',
      error: 'İllegal giriş',
    },
    upload: {
      deleteTip: 'kaldırmak için delete tuşuna bas',
      delete: 'Sil',
      preview: 'Görüntüle',
      continue: 'Devam',
    },
    table: {
      emptyText: 'Veri yok',
      confirmFilter: 'Onayla',
      resetFilter: 'Sıfırla',
      clearFilter: 'Hepsi',
      sumText: 'Sum',
    },
    tree: {
      emptyText: 'Veri yok',
    },
    transfer: {
      noMatch: 'Eşleşen veri bulunamadı',
      noData: 'Veri yok',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Anahtar kelimeleri gir',
      noCheckedFormat: '{total} adet',
      hasCheckedFormat: '{checked}/{total} seçildi',
    },
    image: {
      error: 'BAŞARISIZ OLDU',
    },
    pageHeader: {
      title: 'Geri',
    },
    popconfirm: {
      confirmButtonText: 'Evet',
      cancelButtonText: 'Hayır',
    },
    carousel: {
      leftArrow: 'Carousel arrow left', // to be translated
      rightArrow: 'Carousel arrow right', // to be translated
      indicator: 'Carousel switch to index {index}', // to be translated
    },
  },
}
