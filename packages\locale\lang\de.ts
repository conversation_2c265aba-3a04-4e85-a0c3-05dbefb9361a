export default {
  name: 'de',
  el: {
    breadcrumb: {
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    },
    colorpicker: {
      confirm: 'OK',
      clear: '<PERSON><PERSON>',
    },
    datepicker: {
      now: '<PERSON>zt',
      today: 'Heute',
      cancel: 'Abbrechen',
      clear: '<PERSON><PERSON>',
      confirm: 'OK',
      selectDate: 'Datum wählen',
      selectTime: 'Uhrzeit wählen',
      startDate: 'Startdatum',
      startTime: 'Startzeit',
      endDate: 'Enddatum',
      endTime: 'Endzeit',
      prevYear: 'Letztes Jahr',
      nextYear: 'Nächtes Jahr',
      prevMonth: 'Letzter Monat',
      nextMonth: 'Nächster Monat',
      day: 'Tag',
      week: 'Woche',
      month: '<PERSON><PERSON>',
      year: '',
      month1: 'Januar',
      month2: 'Februar',
      month3: 'M<PERSON>rz',
      month4: 'April',
      month5: 'Mai',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'August',
      month9: 'September',
      month10: 'Okto<PERSON>',
      month11: 'November',
      month12: 'Dezember',
      weeks: {
        sun: 'So',
        mon: 'Mo',
        tue: 'Di',
        wed: 'Mi',
        thu: 'Do',
        fri: 'Fr',
        sat: 'Sa',
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: '<PERSON><PERSON>r',
        apr: 'Apr',
        may: '<PERSON>',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dez',
      },
    },
    select: {
      loading: 'Lädt.',
      noMatch: 'Nichts gefunden.',
      noData: 'Keine Daten',
      placeholder: 'Daten wählen',
    },
    mention: {
      loading: 'Lädt.',
    },
    cascader: {
      noMatch: 'Nichts gefunden.',
      loading: 'Lädt.',
      placeholder: 'Daten wählen',
      noData: 'Keine Daten',
    },
    pagination: {
      goto: 'Gehe zu',
      pagesize: ' pro Seite',
      total: 'Gesamt {total}',
      pageClassifier: '',
      page: 'Seite',
      prev: 'Zur vorherigen Seite gehen',
      next: 'Zur nächsten Seite gehen',
      currentPage: 'Seite {pager}',
      prevPages: 'Vorherige {pager} Seiten',
      nextPages: 'Nächste {pager} Seiten',
    },
    messagebox: {
      confirm: 'OK',
      cancel: 'Abbrechen',
      error: 'Fehler',
    },
    upload: {
      deleteTip: 'Klicke löschen zum entfernen',
      delete: 'Löschen',
      preview: 'Vorschau',
      continue: 'Fortsetzen',
    },
    table: {
      emptyText: 'Keine Daten',
      confirmFilter: 'Anwenden',
      resetFilter: 'Zurücksetzen',
      clearFilter: 'Alles ',
      sumText: 'Summe',
    },
    tour: {
      next: 'Weiter',
      previous: 'Zurück',
      finish: 'Fertig',
    },
    tree: {
      emptyText: 'Keine Einträge',
    },
    transfer: {
      noMatch: 'Nichts gefunden.',
      noData: 'Keine Einträge',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Einträge filtern',
      noCheckedFormat: '{total} Einträge',
      hasCheckedFormat: '{checked}/{total} ausgewählt',
    },
    image: {
      error: 'FEHLGESCHLAGEN',
    },
    pageHeader: {
      title: 'Zurück',
    },
    popconfirm: {
      confirmButtonText: 'Ja',
      cancelButtonText: 'Nein',
    },
    carousel: {
      leftArrow: 'Karussell-Pfeil links',
      rightArrow: 'Karussell-Pfeil rechts',
      indicator: 'Karussell zu Index {index} wechseln',
    },
  },
}
