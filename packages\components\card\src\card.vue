<template>
  <el-card>
    <template v-if="$slots.header || header">
      <slot name="header">{{ header }}</slot>
    </template>
    <slot />
    <template v-if="$slots.footer || footer">
      <slot name="footer">{{ footer }}</slot>
    </template>
  </el-card>
</template>

<script lang="ts" setup>
import { neCardProps } from './card'

defineOptions({
  name: 'NeCard',
})

defineProps(neCardProps)
</script>
