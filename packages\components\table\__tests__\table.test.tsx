import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import Table from '../src/table.vue'

const AXIOM = '<PERSON><PERSON> is the best girl'

describe('Table.vue', () => {
  test('render test', () => {
    const wrapper = mount(Table as any, {
      props: { data: [] },
      slots: { default: AXIOM },
    })
    expect(wrapper.text()).toContain(AXIOM)
  })

  test('should render table with data', () => {
    const tableData = [
      { name: '<PERSON>', age: 20 },
      { name: '<PERSON>', age: 18 },
    ]

    const wrapper = mount(Table as any, {
      props: { data: tableData },
      slots: {
        default: `
          <el-table-column prop="name" label="Name" />
          <el-table-column prop="age" label="Age" />
        `,
      },
    })

    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('should pass props to el-table', () => {
    const wrapper = mount(Table as any, {
      props: { data: [], stripe: true, border: true },
    })

    expect(wrapper.find('.el-table--striped').exists()).toBe(true)
    expect(wrapper.find('.el-table--border').exists()).toBe(true)
  })

  test('should support bordered prop', () => {
    const wrapper = mount(Table as any, {
      props: { data: [], bordered: true },
    })

    expect(wrapper.find('.el-table').exists()).toBe(true)
    expect(wrapper.find('.ne-table--bordered').exists()).toBe(true)
  })
})
